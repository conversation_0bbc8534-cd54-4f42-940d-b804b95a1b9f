import { en } from './en'
import { ar } from './ar'
import type { Locale } from '@/types'

export const locales = {
  en,
  ar,
} as const

export type LocaleKey = keyof typeof locales

export function getLocaleData(locale: Locale) {
  return locales[locale] || locales.en
}

export function t(locale: Locale, key: string, params?: Record<string, string | number>): string {
  const data = getLocaleData(locale)
  const keys = key.split('.')
  
  let value: any = data
  for (const k of keys) {
    value = value?.[k]
  }
  
  if (typeof value !== 'string') {
    console.warn(`Translation key "${key}" not found for locale "${locale}"`)
    return key
  }
  
  if (params) {
    return value.replace(/\{(\w+)\}/g, (match: string, paramKey: string) => {
      return params[paramKey]?.toString() || match
    })
  }
  
  return value
}

export { en, ar }