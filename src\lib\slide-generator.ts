import { SlideTemplate, getTemplate } from './slide-templates'
import { AdvancedSlideGenerator, PresentationData, SlideData, ChartData } from './slide-generator-advanced'

export interface SlideContent {
  type: 'title' | 'content' | 'conclusion'
  title: string
  content?: string
  subtitle?: string
  author?: string
  date?: string
  institution?: string
  slideNumber?: number
}

// Legacy interface for backward compatibility
export interface LegacyPresentationData {
  title: string
  subtitle?: string
  author?: string
  date?: string
  institution?: string
  slides: SlideContent[]
  templateId: string
}

export class SlideGenerator {
  private template: SlideTemplate

  constructor(templateId: string = 'modern-business') {
    const template = getTemplate(templateId)
    if (!template) {
      throw new Error(`Template ${templateId} not found`)
    }
    this.template = template
  }

  generatePresentation(data: LegacyPresentationData): string {
    const { title, subtitle, author, date, institution, slides } = data
    
    // Generate complete HTML document
    const html = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${title}</title>
    <style>
        ${this.template.css}
        
        /* Additional presentation styles */
        .presentation-container {
            position: relative;
            overflow: hidden;
        }
        
        .slide {
            display: none;
        }
        
        .slide.active {
            display: flex;
        }
        
        .navigation {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 1000;
            display: flex;
            gap: 10px;
            background: rgba(0, 0, 0, 0.7);
            padding: 10px 20px;
            border-radius: 25px;
            backdrop-filter: blur(10px);
        }
        
        .nav-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            padding: 8px 16px;
            border-radius: 15px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .nav-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        
        .slide-indicator {
            color: white;
            font-size: 14px;
            display: flex;
            align-items: center;
            margin: 0 10px;
        }
        
        /* Animations */
        .slide.fade-in {
            animation: fadeIn 0.5s ease-in-out;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        /* Print styles */
        @media print {
            .slide {
                display: flex !important;
                page-break-after: always;
                width: 100vw;
                height: 100vh;
            }
            
            .navigation {
                display: none;
            }
        }
        
        /* Responsive design */
        @media (max-width: 768px) {
            .slide {
                padding: 40px 20px;
            }
            
            .slide h1 {
                font-size: 2.5rem;
            }
            
            .slide h2 {
                font-size: 2rem;
            }
            
            .slide p {
                font-size: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="presentation-container">
        ${this.generateSlides(slides, { title, subtitle, author, date, institution })}
        
        <div class="navigation">
            <button class="nav-btn" onclick="previousSlide()" id="prevBtn">← Previous</button>
            <div class="slide-indicator">
                <span id="currentSlide">1</span> / <span id="totalSlides">${slides.length}</span>
            </div>
            <button class="nav-btn" onclick="nextSlide()" id="nextBtn">Next →</button>
            <button class="nav-btn" onclick="toggleFullscreen()">⛶ Fullscreen</button>
        </div>
    </div>

    <script>
        let currentSlideIndex = 0;
        const slides = document.querySelectorAll('.slide');
        const totalSlides = slides.length;
        
        function showSlide(index) {
            slides.forEach((slide, i) => {
                slide.classList.remove('active', 'fade-in');
                if (i === index) {
                    slide.classList.add('active', 'fade-in');
                }
            });
            
            document.getElementById('currentSlide').textContent = index + 1;
            document.getElementById('prevBtn').disabled = index === 0;
            document.getElementById('nextBtn').disabled = index === totalSlides - 1;
        }
        
        function nextSlide() {
            if (currentSlideIndex < totalSlides - 1) {
                currentSlideIndex++;
                showSlide(currentSlideIndex);
            }
        }
        
        function previousSlide() {
            if (currentSlideIndex > 0) {
                currentSlideIndex--;
                showSlide(currentSlideIndex);
            }
        }
        
        function toggleFullscreen() {
            if (!document.fullscreenElement) {
                document.documentElement.requestFullscreen();
            } else {
                document.exitFullscreen();
            }
        }
        
        // Keyboard navigation
        document.addEventListener('keydown', (e) => {
            switch(e.key) {
                case 'ArrowRight':
                case ' ':
                    e.preventDefault();
                    nextSlide();
                    break;
                case 'ArrowLeft':
                    e.preventDefault();
                    previousSlide();
                    break;
                case 'f':
                case 'F11':
                    e.preventDefault();
                    toggleFullscreen();
                    break;
                case 'Home':
                    e.preventDefault();
                    currentSlideIndex = 0;
                    showSlide(currentSlideIndex);
                    break;
                case 'End':
                    e.preventDefault();
                    currentSlideIndex = totalSlides - 1;
                    showSlide(currentSlideIndex);
                    break;
            }
        });
        
        // Initialize
        showSlide(0);
        
        // Auto-advance slides (optional)
        let autoAdvance = false;
        let autoAdvanceInterval;
        
        function startAutoAdvance(seconds = 10) {
            autoAdvance = true;
            autoAdvanceInterval = setInterval(() => {
                if (currentSlideIndex < totalSlides - 1) {
                    nextSlide();
                } else {
                    stopAutoAdvance();
                }
            }, seconds * 1000);
        }
        
        function stopAutoAdvance() {
            autoAdvance = false;
            if (autoAdvanceInterval) {
                clearInterval(autoAdvanceInterval);
            }
        }
        
        // Stop auto-advance on user interaction
        document.addEventListener('click', stopAutoAdvance);
        document.addEventListener('keydown', stopAutoAdvance);
    </script>
</body>
</html>`

    return html
  }

  private generateSlides(slides: SlideContent[], metadata: any): string {
    return slides.map((slide, index) => {
      const slideData = {
        ...slide,
        ...metadata,
        slideNumber: index + 1
      }
      
      switch (slide.type) {
        case 'title':
          return this.generateSlideFromTemplate(this.template.structure.titleSlide, slideData)
        case 'conclusion':
          return this.generateSlideFromTemplate(this.template.structure.conclusionSlide, slideData)
        default:
          return this.generateSlideFromTemplate(this.template.structure.contentSlide, slideData)
      }
    }).join('\n')
  }

  private generateSlideFromTemplate(template: string, data: any): string {
    let html = template
    
    // Replace placeholders
    Object.keys(data).forEach(key => {
      const placeholder = `{{${key}}}`
      const value = data[key] || ''
      html = html.replace(new RegExp(placeholder, 'g'), value)
    })
    
    return html
  }

  // Generate fallback presentation when AI is not available
  static generateFallbackPresentation(topic: string, templateId: string = 'arabic-professional'): string {
    // Use the advanced generator for better results
    try {
      return AdvancedSlideGenerator.generateFallbackPresentation(topic, templateId)
    } catch (error) {
      // Advanced generator failed, using basic fallback

      // Fallback to original method
      const generator = new SlideGenerator(templateId === 'arabic-professional' ? 'modern-business' : templateId)

      // Generate topic-specific content
      const topicContent = this.generateTopicContent(topic)

      const fallbackData: LegacyPresentationData = {
        title: topicContent.title,
        subtitle: topicContent.subtitle,
        author: 'WIDDX AI',
        date: new Date().toLocaleDateString(),
        templateId: templateId === 'arabic-professional' ? 'modern-business' : templateId,
        slides: topicContent.slides
      }

      return generator.generatePresentation(fallbackData)
    }
  }

  private static generateTopicContent(topic: string): {
    title: string
    subtitle: string
    slides: SlideContent[]
  } {
    const lowerTopic = topic.toLowerCase()

    // AI and Machine Learning specific content
    if (lowerTopic.includes('artificial intelligence') || lowerTopic.includes('machine learning') || lowerTopic.includes('ai')) {
      return {
        title: 'Artificial Intelligence & Machine Learning',
        subtitle: 'The Future of Technology',
        slides: [
          {
            type: 'title',
            title: 'Artificial Intelligence & Machine Learning',
            subtitle: 'The Future of Technology'
          },
          {
            type: 'content',
            title: 'What is Artificial Intelligence?',
            content: `
              <p>Artificial Intelligence (AI) is the simulation of human intelligence in machines that are programmed to think and learn like humans.</p>
              <ul>
                <li><strong>Machine Learning:</strong> Algorithms that improve through experience</li>
                <li><strong>Deep Learning:</strong> Neural networks with multiple layers</li>
                <li><strong>Natural Language Processing:</strong> Understanding human language</li>
                <li><strong>Computer Vision:</strong> Interpreting visual information</li>
              </ul>
            `
          },
          {
            type: 'content',
            title: 'Types of Machine Learning',
            content: `
              <h3>Supervised Learning</h3>
              <p>Learning with labeled training data to make predictions</p>

              <h3>Unsupervised Learning</h3>
              <p>Finding patterns in data without labeled examples</p>

              <h3>Reinforcement Learning</h3>
              <p>Learning through interaction with environment and rewards</p>
            `
          },
          {
            type: 'content',
            title: 'Real-World Applications',
            content: `
              <ul>
                <li><strong>Healthcare:</strong> Medical diagnosis and drug discovery</li>
                <li><strong>Transportation:</strong> Autonomous vehicles and traffic optimization</li>
                <li><strong>Finance:</strong> Fraud detection and algorithmic trading</li>
                <li><strong>Entertainment:</strong> Recommendation systems and content creation</li>
                <li><strong>Education:</strong> Personalized learning and intelligent tutoring</li>
              </ul>
            `
          },
          {
            type: 'content',
            title: 'Benefits and Challenges',
            content: `
              <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem;">
                <div>
                  <h3 style="color: #059669;">Benefits</h3>
                  <ul>
                    <li>Increased efficiency</li>
                    <li>Better decision making</li>
                    <li>24/7 availability</li>
                    <li>Cost reduction</li>
                  </ul>
                </div>
                <div>
                  <h3 style="color: #dc2626;">Challenges</h3>
                  <ul>
                    <li>Ethical considerations</li>
                    <li>Job displacement</li>
                    <li>Data privacy</li>
                    <li>Bias in algorithms</li>
                  </ul>
                </div>
              </div>
            `
          },
          {
            type: 'conclusion',
            title: 'The Future of AI',
            content: `
              <p>Artificial Intelligence and Machine Learning are transforming every aspect of our lives and work.</p>
              <p>As we advance, it's crucial to develop AI responsibly, ensuring it benefits humanity while addressing ethical concerns.</p>
              <div style="margin-top: 2rem; padding: 1.5rem; background: linear-gradient(135deg, #667eea20, #764ba220); border-radius: 12px;">
                <p style="font-size: 1.1rem; margin: 0;"><strong>Key Takeaway:</strong> AI is not just about technology—it's about augmenting human capabilities and solving complex problems.</p>
              </div>
            `
          }
        ]
      }
    }

    // Renewable Energy content
    if (lowerTopic.includes('renewable energy') || lowerTopic.includes('clean energy') || lowerTopic.includes('solar') || lowerTopic.includes('wind')) {
      return {
        title: 'Renewable Energy Solutions',
        subtitle: 'Powering a Sustainable Future',
        slides: [
          {
            type: 'title',
            title: 'Renewable Energy Solutions',
            subtitle: 'Powering a Sustainable Future'
          },
          {
            type: 'content',
            title: 'What is Renewable Energy?',
            content: `
              <p>Renewable energy comes from natural sources that are constantly replenished and never run out.</p>
              <ul>
                <li><strong>Solar Energy:</strong> Harnessing power from the sun</li>
                <li><strong>Wind Energy:</strong> Converting wind motion into electricity</li>
                <li><strong>Hydroelectric:</strong> Using flowing water to generate power</li>
                <li><strong>Geothermal:</strong> Utilizing Earth's internal heat</li>
                <li><strong>Biomass:</strong> Energy from organic materials</li>
              </ul>
            `
          },
          {
            type: 'content',
            title: 'Environmental Benefits',
            content: `
              <ul>
                <li><strong>Zero Emissions:</strong> No greenhouse gases during operation</li>
                <li><strong>Air Quality:</strong> Reduces pollution and smog</li>
                <li><strong>Climate Change:</strong> Helps combat global warming</li>
                <li><strong>Sustainability:</strong> Preserves resources for future generations</li>
                <li><strong>Biodiversity:</strong> Less impact on ecosystems</li>
              </ul>
            `
          },
          {
            type: 'content',
            title: 'Economic Advantages',
            content: `
              <ul>
                <li><strong>Job Creation:</strong> Growing employment opportunities</li>
                <li><strong>Energy Independence:</strong> Reduced reliance on imports</li>
                <li><strong>Stable Prices:</strong> Protection from fuel price volatility</li>
                <li><strong>Innovation:</strong> Driving technological advancement</li>
                <li><strong>Investment:</strong> Attracting capital and development</li>
              </ul>
            `
          },
          {
            type: 'conclusion',
            title: 'The Path Forward',
            content: `
              <p>Renewable energy is essential for a sustainable future and combating climate change.</p>
              <p>Continued investment and innovation will make clean energy more accessible and affordable for everyone.</p>
            `
          }
        ]
      }
    }

    // Generic fallback content
    return {
      title: topic,
      subtitle: 'Comprehensive Overview',
      slides: [
        {
          type: 'title',
          title: topic,
          subtitle: 'Comprehensive Overview'
        },
        {
          type: 'content',
          title: 'Introduction',
          content: `
            <p>This presentation provides a comprehensive overview of <span class="highlight">${topic}</span>.</p>
            <ul>
              <li>Background and context</li>
              <li>Key concepts and principles</li>
              <li>Current applications</li>
              <li>Future implications</li>
            </ul>
          `
        },
        {
          type: 'content',
          title: 'Key Concepts',
          content: `
            <p>Understanding the fundamental aspects of ${topic}:</p>
            <ul>
              <li>Core principles and foundations</li>
              <li>Important terminology and definitions</li>
              <li>Historical development and evolution</li>
              <li>Current state and trends</li>
            </ul>
          `
        },
        {
          type: 'content',
          title: 'Applications & Impact',
          content: `
            <p>Real-world applications and significance of ${topic}:</p>
            <ul>
              <li>Practical implementations</li>
              <li>Industry applications</li>
              <li>Social and economic impact</li>
              <li>Benefits and advantages</li>
            </ul>
          `
        },
        {
          type: 'conclusion',
          title: 'Conclusion',
          content: `
            <p>In conclusion, ${topic} represents a significant area of study and application.</p>
            <p>Understanding these concepts provides valuable insights for future development and implementation.</p>
          `
        }
      ]
    }
  }
}

export default SlideGenerator
