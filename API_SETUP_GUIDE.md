# WIDDX AI - API Setup Guide

## 🔧 Quick Fix for Backend AI Models Issue

If you're seeing messages about "backend AI models integration issue" or configuration required, follow these steps:

## 📋 Prerequisites

You need API keys from at least one of these providers:
- **DeepSeek** (Recommended - Free tier available)
- **Google Gemini** (Free tier available)

## 🚀 Step-by-Step Setup

### Option 1: DeepSeek API (Recommended)

1. **Get DeepSeek API Key:**
   - Visit: https://platform.deepseek.com/
   - Sign up for a free account
   - Go to API Keys section
   - Create a new API key
   - Copy the key (starts with `sk-`)

2. **Add to Environment:**
   - Open `.env.local` file in your project root
   - Replace the demo key:
   ```env
   DEEPSEEK_API_KEY=sk-your-actual-deepseek-key-here
   ```

### Option 2: Google Gemini API

1. **Get Gemini API Key:**
   - Visit: https://aistudio.google.com/
   - Sign in with Google account
   - Click "Get API Key"
   - Create a new API key
   - Copy the key (starts with `<PERSON><PERSON>`)

2. **Add to Environment:**
   - Open `.env.local` file in your project root
   - Replace the demo key:
   ```env
   GEMINI_API_KEY=AIza-your-actual-gemini-key-here
   ```

### Option 3: Both APIs (Best Performance)

For maximum reliability, configure both:

```env
# API Keys
DEEPSEEK_API_KEY=sk-your-deepseek-key-here
GEMINI_API_KEY=AIza-your-gemini-key-here
```

## 🔄 Apply Changes

1. **Save the `.env.local` file**
2. **Restart the development server:**
   ```bash
   # Stop the current server (Ctrl+C)
   # Then restart:
   npm run dev
   ```
3. **Test the application** - Try sending a message

## ✅ Verification

After setup, you should see:
- ✅ Messages are processed by AI
- ✅ No more "configuration required" messages
- ✅ All chat modes work properly

## 🆓 Free Tier Limits

### DeepSeek
- **Free Credits:** $5 worth of API calls
- **Rate Limits:** 60 requests/minute
- **Models:** deepseek-chat, deepseek-reasoner

### Gemini
- **Free Tier:** 15 requests/minute
- **Rate Limits:** 1,500 requests/day
- **Models:** gemini-1.5-flash, gemini-1.5-pro

## 🔍 Troubleshooting

### Issue: "404 - Unknown error" (DeepSeek)
- **Cause:** Invalid API key or incorrect URL
- **Fix:** Double-check your API key in `.env.local`

### Issue: "Quota exceeded" (Gemini)
- **Cause:** Free tier limit reached
- **Fix:** Wait for quota reset or upgrade to paid tier

### Issue: Both services failing
- **Cause:** Network issues or both APIs down
- **Fix:** Check internet connection and API status pages

## 📞 Support

- **DeepSeek Status:** https://status.deepseek.com/
- **Gemini Status:** Check Google AI Studio
- **Project Issues:** Check the terminal for detailed error messages

## 🎯 Next Steps

Once configured, you can:
- Chat with AI in multiple languages
- Generate code with syntax highlighting
- Create professional presentations
- Use writing assistance
- Perform research and summarization

---

**WIDDX AI** - Professional AI Assistant
