'use client'

import { useEffect, useRef, useState } from 'react'
import { WelcomeScreen } from './WelcomeScreen'
import { MessageList } from './MessageList'
import { ChatInput } from './ChatInput'
import { TypingIndicator } from './TypingIndicator'
import { t } from '@/locales'
import type { Chat, ChatMode, Locale } from '@/types'

interface ChatAreaProps {
  currentChat: Chat | null
  currentMode: ChatMode
  isLoading: boolean
  isTyping: boolean
  onSendMessage: (content: string) => Promise<void>
  onRegenerateResponse: () => Promise<void>
  onClearChat: () => void
  onModeSelect: (mode: ChatMode) => void
  onOpenSlidePanel?: (content: string, title?: string) => void
  locale: Locale
}

export function ChatArea({
  currentChat,
  currentMode,
  isLoading,
  isTyping,
  onSendMessage,
  onRegenerateResponse,
  onClearChat,
  onModeSelect,
  onOpenSlidePanel,
  locale
}: ChatAreaProps) {
  const messagesEndRef = useRef<HTMLDivElement>(null)

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [currentChat?.messages, isTyping])

  const hasMessages = currentChat && currentChat.messages.length > 0

  return (
    <div className="h-full flex flex-col bg-light-primary dark:bg-dark-primary overflow-hidden">
      {!hasMessages ? (
        <div className="flex-1 overflow-y-auto">
          <WelcomeScreen
            onModeSelect={onModeSelect}
            onPromptSelect={onSendMessage}
            currentMode={currentMode}
            locale={locale}
          />
        </div>
      ) : (
        <div className="flex-1 overflow-y-auto min-h-0">
          <div className="max-w-4xl mx-auto px-4 py-6">
            <MessageList
              messages={currentChat.messages}
              onRegenerateResponse={onRegenerateResponse}
              onOpenSlidePanel={onOpenSlidePanel}
              locale={locale}
            />

            {/* Typing Indicator */}
            {isTyping && (
              <div className="mt-6">
                <TypingIndicator isVisible={true} />
              </div>
            )}

            {/* Scroll anchor */}
            <div ref={messagesEndRef} />
          </div>
        </div>
      )}

      {/* Input Area - Fixed at bottom */}
      <div className="flex-shrink-0 border-t border-light-border dark:border-dark-border bg-light-secondary dark:bg-dark-secondary">
        <div className="max-w-4xl mx-auto px-4 py-4">
          <ChatInput
            onSendMessage={onSendMessage}
            disabled={isLoading}
            placeholder={t(locale, `modes.${currentMode}.placeholder`)}
            maxLength={4000}
            currentMode={currentMode}
            locale={locale}
          />

          {/* Disclaimer */}
          <div className="mt-3 text-center">
            <p className="text-xs text-light-text-muted dark:text-dark-text-muted">
              {t(locale, 'chat.disclaimer')}
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}