'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { X, Code, Eye, Download, ChevronLeft, ChevronRight, Copy, FileText } from 'lucide-react'
import type { Locale } from '@/types'
import { exportPresentationToPDF, PDFExporter } from '@/lib/pdf-exporter'

interface SlidePanelProps {
  isOpen: boolean
  onClose: () => void
  content: string
  title: string
  locale: Locale
  onTemplateChange?: (templateId: string) => void
}

export function SlidePanel({ isOpen, onClose, content, title, locale, onTemplateChange }: SlidePanelProps) {
  const [activeTab, setActiveTab] = useState<'preview' | 'html'>('preview')
  const [currentSlide, setCurrentSlide] = useState(1)
  const [totalSlides, setTotalSlides] = useState(1)
  const [isExportingPDF, setIsExportingPDF] = useState(false)
  const [isFullscreen, setIsFullscreen] = useState(false)

  useEffect(() => {
    if (content) {
      const slideMatches = content.match(/<div[^>]*class[^>]*slide[^>]*>/gi) ||
                          content.match(/<section[^>]*>/gi) ||
                          content.match(/<div[^>]*class[^>]*page[^>]*>/gi)
      setTotalSlides(slideMatches ? slideMatches.length : 1)
      setCurrentSlide(1)
    }
  }, [content])

  const handleCopyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(content)
    } catch (err) {
      console.error('Failed to copy to clipboard:', err)
    }
  }

  const handleDownloadSlides = () => {
    const blob = new Blob([content], { type: 'text/html' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `${title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.html`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  const handleExportToPDF = async () => {
    if (isExportingPDF) return

    setIsExportingPDF(true)
    try {
      // Ensure PDF libraries are loaded
      if (!PDFExporter.isPDFExportAvailable()) {
        await PDFExporter.loadPDFLibraries()
      }

      const filename = `${title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.pdf`
      await exportPresentationToPDF(content, filename, {
        format: 'Slide',
        orientation: 'landscape',
        quality: 1.0,
        includeCharts: true
      })
    } catch (error) {
      console.error('PDF export failed:', error)
      alert(locale === 'ar' ? 'فشل في تصدير PDF. يرجى المحاولة مرة أخرى.' : 'PDF export failed. Please try again.')
    } finally {
      setIsExportingPDF(false)
    }
  }

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen)
  }

  const handleNextSlide = () => {
    if (currentSlide < totalSlides) {
      setCurrentSlide(prev => prev + 1)
    }
  }

  const handlePrevSlide = () => {
    if (currentSlide > 1) {
      setCurrentSlide(prev => prev - 1)
    }
  }

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ x: '100%' }}
          animate={{ x: 0 }}
          exit={{ x: '100%' }}
          transition={{ type: 'spring', damping: 25, stiffness: 200 }}
          className="fixed lg:relative top-0 right-0 w-full lg:w-1/2 h-full lg:h-auto bg-white dark:bg-gray-900 border-l border-gray-200 dark:border-gray-700 flex flex-col shadow-2xl overflow-hidden z-50 lg:z-auto"
        >
          <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
            <div className="flex items-center space-x-3">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white truncate">
                {title || 'Presentation'}
              </h3>
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={handleCopyToClipboard}
                className="flex items-center space-x-2 px-3 py-2 text-sm bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
              >
                <Copy className="w-4 h-4" />
                <span>Copy</span>
              </button>
              <button
                onClick={handleDownloadSlides}
                className="flex items-center space-x-2 px-3 py-2 text-sm bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                <Download className="w-4 h-4" />
                <span>HTML</span>
              </button>
              <button
                onClick={handleExportToPDF}
                disabled={isExportingPDF}
                className="flex items-center space-x-2 px-3 py-2 text-sm bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <FileText className="w-4 h-4" />
                <span>{isExportingPDF ? (locale === 'ar' ? 'جاري التصدير...' : 'Exporting...') : 'PDF'}</span>
              </button>
              <button
                onClick={onClose}
                className="p-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
          </div>

          <div className="flex border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800">
            <button
              onClick={() => setActiveTab('preview')}
              className={`flex items-center space-x-2 px-4 py-3 text-sm font-medium transition-colors ${
                activeTab === 'preview'
                  ? 'text-blue-600 border-b-2 border-blue-600 bg-white dark:bg-gray-900'
                  : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200'
              }`}
            >
              <Eye className="w-4 h-4" />
              <span>Preview</span>
            </button>
            <button
              onClick={() => setActiveTab('html')}
              className={`flex items-center space-x-2 px-4 py-3 text-sm font-medium transition-colors ${
                activeTab === 'html'
                  ? 'text-blue-600 border-b-2 border-blue-600 bg-white dark:bg-gray-900'
                  : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200'
              }`}
            >
              <Code className="w-4 h-4" />
              <span>HTML</span>
            </button>
          </div>

          <div className="flex-1 overflow-hidden">
            {activeTab === 'preview' ? (
              <div className={`h-full flex flex-col ${isFullscreen ? 'fixed inset-0 z-50 bg-white dark:bg-gray-900' : ''}`}>
                {totalSlides > 1 && (
                  <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
                    <button
                      onClick={handlePrevSlide}
                      disabled={currentSlide === 1}
                      className="p-2 rounded-lg bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors"
                    >
                      <ChevronLeft className="w-4 h-4" />
                    </button>
                    <span className="text-sm text-gray-600 dark:text-gray-400">
                      {currentSlide} / {totalSlides}
                    </span>
                    <button
                      onClick={handleNextSlide}
                      disabled={currentSlide === totalSlides}
                      className="p-2 rounded-lg bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors"
                    >
                      <ChevronRight className="w-4 h-4" />
                    </button>
                    <button
                      onClick={toggleFullscreen}
                      className="p-2 rounded-lg bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors"
                    >
                      {isFullscreen ? 'Exit Fullscreen' : 'Fullscreen'}
                    </button>
                  </div>
                )}

                <div className="flex-1 p-2 sm:p-4 overflow-auto">
                  <div className="w-full h-full bg-white rounded-lg shadow-lg overflow-hidden transform transition-all duration-300 hover:scale-105">
                    <iframe
                      srcDoc={content}
                      className="w-full h-full border-0"
                      title="Slide Preview"
                      sandbox="allow-scripts allow-same-origin"
                      style={{
                        minHeight: '400px',
                        maxWidth: '100%',
                        aspectRatio: '16/9'
                      }}
                    />
                  </div>
                </div>
              </div>
            ) : (
              <div className="h-full p-4 overflow-auto">
                <pre className="text-sm text-gray-800 dark:text-gray-200 whitespace-pre-wrap font-mono bg-gray-50 dark:bg-gray-800 p-4 rounded-lg html-content word-wrap break-word overflow-wrap break-word max-width-full">
                  {content}
                </pre>
              </div>
            )}
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  )
}

export default SlidePanel
