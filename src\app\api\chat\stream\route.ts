import { NextRequest, NextResponse } from 'next/server'
import { AIManager } from '@/lib/ai-manager'
import type { Message, ChatMode, Locale } from '@/types'

// Initialize AI Manager
const aiManager = new AIManager()

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { 
      messages, 
      mode = 'chat', 
      locale = 'en', 
      userId 
    }: {
      messages: Message[]
      mode: ChatMode
      locale: Locale
      userId?: string
    } = body

    // Validate input
    if (!messages || !Array.isArray(messages)) {
      return NextResponse.json(
        { error: 'Messages array is required' },
        { status: 400 }
      )
    }

    if (messages.length === 0) {
      return NextResponse.json(
        { error: 'At least one message is required' },
        { status: 400 }
      )
    }

    // Generate streaming response
    const result = await aiManager.generateResponse(
      messages,
      mode,
      locale,
      userId,
      true // Enable streaming
    )

    if ('stream' in result) {
      // Create a custom readable stream that formats the data for SSE
      const encoder = new TextEncoder()
      const stream = new ReadableStream({
        async start(controller) {
          const reader = result.stream.getReader()
          
          try {
            // Send initial metadata
            controller.enqueue(
              encoder.encode(`data: ${JSON.stringify({
                type: 'metadata',
                model: result.model,
                provider: result.provider
              })}\n\n`)
            )

            while (true) {
              const { done, value } = await reader.read()
              
              if (done) {
                // Send completion signal
                controller.enqueue(
                  encoder.encode(`data: ${JSON.stringify({
                    type: 'done'
                  })}\n\n`)
                )
                controller.close()
                break
              }

              // Send content chunk
              const content = new TextDecoder().decode(value)
              controller.enqueue(
                encoder.encode(`data: ${JSON.stringify({
                  type: 'content',
                  content
                })}\n\n`)
              )
            }
          } catch (error) {
            // Send error
            controller.enqueue(
              encoder.encode(`data: ${JSON.stringify({
                type: 'error',
                error: error instanceof Error ? error.message : 'Stream error'
              })}\n\n`)
            )
            controller.close()
          }
        }
      })

      return new Response(stream, {
        headers: {
          'Content-Type': 'text/event-stream',
          'Cache-Control': 'no-cache',
          'Connection': 'keep-alive',
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'POST',
          'Access-Control-Allow-Headers': 'Content-Type',
        },
      })
    } else {
      // Fallback to regular response if streaming failed
      return NextResponse.json({
        success: true,
        data: result
      })
    }

  } catch (error) {
    console.error('Stream API Error:', error)
    
    return NextResponse.json(
      { 
        success: false,
        error: error instanceof Error ? error.message : 'Internal server error' 
      },
      { status: 500 }
    )
  }
}
