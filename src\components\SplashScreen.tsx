'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'

export function SplashScreen() {
  const [progress, setProgress] = useState(0)

  useEffect(() => {
    const interval = setInterval(() => {
      setProgress(prev => {
        const increment = Math.random() * 15 + 5
        const newProgress = Math.min(prev + increment, 100)
        
        if (newProgress >= 100) {
          clearInterval(interval)
        }
        
        return newProgress
      })
    }, 150)

    return () => clearInterval(interval)
  }, [])

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-light-primary dark:bg-dark-primary">
      <div className="flex flex-col items-center space-y-8">
        {/* Logo */}
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5 }}
          className="relative"
        >
          <div className="w-24 h-24 relative">
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
              className="absolute inset-0"
            >
              <svg
                viewBox="0 0 100 100"
                className="w-full h-full text-light-accent dark:text-dark-accent"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <circle
                  cx="50"
                  cy="50"
                  r="45"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeDasharray="10 5"
                  className="opacity-30"
                />
              </svg>
            </motion.div>
            
            <div className="absolute inset-0 flex items-center justify-center">
              <svg
                viewBox="0 0 100 100"
                className="w-16 h-16 text-light-accent dark:text-dark-accent"
                fill="currentColor"
                xmlns="http://www.w3.org/2000/svg"
              >
                <circle cx="35" cy="40" r="3" />
                <circle cx="65" cy="40" r="3" />
                <path
                  d="M30 60 Q50 75 70 60"
                  stroke="currentColor"
                  strokeWidth="2"
                  fill="none"
                />
                <text
                  x="50"
                  y="25"
                  textAnchor="middle"
                  fontSize="10"
                  fontWeight="bold"
                  className="fill-current"
                >
                  WIDDX
                </text>
              </svg>
            </div>
          </div>
        </motion.div>

        {/* Progress Bar */}
        <div className="w-96 max-w-[90vw]">
          <div className="relative">
            <div className="w-full h-3 bg-light-border dark:bg-dark-border rounded-full overflow-hidden">
              <motion.div
                className="h-full bg-gradient-to-r from-light-accent to-light-accent-hover dark:from-dark-accent dark:to-dark-accent-hover rounded-full"
                initial={{ width: 0 }}
                animate={{ width: `${progress}%` }}
                transition={{ duration: 0.3, ease: "easeOut" }}
              />
            </div>
            
            {/* Progress Text */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.5 }}
              className="mt-4 text-center text-sm text-light-text-secondary dark:text-dark-text-secondary"
            >
              Initializing WIDDX AI... {Math.round(progress)}%
            </motion.div>
          </div>
        </div>

        {/* Loading Animation */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 1 }}
          className="flex space-x-1"
        >
          {[0, 1, 2].map((i) => (
            <motion.div
              key={i}
              className="w-2 h-2 bg-light-accent dark:bg-dark-accent rounded-full"
              animate={{
                scale: [1, 1.2, 1],
                opacity: [0.5, 1, 0.5],
              }}
              transition={{
                duration: 1.5,
                repeat: Infinity,
                delay: i * 0.2,
              }}
            />
          ))}
        </motion.div>
      </div>
    </div>
  )
}