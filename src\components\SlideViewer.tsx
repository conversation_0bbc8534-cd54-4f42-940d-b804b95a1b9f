'use client'

import { useState, useEffect, useRef } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  X, 
  ChevronLeft, 
  ChevronRight, 
  Play, 
  Pause, 
  RotateCcw,
  Maximize2,
  Download,
  Share2
} from 'lucide-react'

interface SlideViewerProps {
  isOpen: boolean
  onClose: () => void
  htmlContent: string
  title?: string
}

export function SlideViewer({ isOpen, onClose, htmlContent, title }: SlideViewerProps) {
  const [currentSlide, setCurrentSlide] = useState(0)
  const [slides, setSlides] = useState<string[]>([])
  const [isPlaying, setIsPlaying] = useState(false)
  const [isFullscreen, setIsFullscreen] = useState(false)
  const iframeRef = useRef<HTMLIFrameElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)

  // Extract slides from HTML content
  useEffect(() => {
    if (htmlContent) {
      // Split content by slide markers or extract multiple HTML documents
      const slidePattern = /<!DOCTYPE html>[\s\S]*?<\/html>/gi
      const extractedSlides = htmlContent.match(slidePattern) || [htmlContent]
      setSlides(extractedSlides)
      setCurrentSlide(0)
    }
  }, [htmlContent])

  // Auto-play functionality
  useEffect(() => {
    if (isPlaying && slides.length > 1) {
      const interval = setInterval(() => {
        setCurrentSlide(prev => (prev + 1) % slides.length)
      }, 5000) // 5 seconds per slide
      return () => clearInterval(interval)
    }
  }, [isPlaying, slides.length])

  // Keyboard navigation
  useEffect(() => {
    const handleKeyPress = (e: KeyboardEvent) => {
      if (!isOpen) return
      
      switch (e.key) {
        case 'ArrowLeft':
          e.preventDefault()
          goToPrevSlide()
          break
        case 'ArrowRight':
          e.preventDefault()
          goToNextSlide()
          break
        case 'Escape':
          e.preventDefault()
          if (isFullscreen) {
            exitFullscreen()
          } else {
            onClose()
          }
          break
        case ' ':
          e.preventDefault()
          togglePlayPause()
          break
      }
    }

    if (isOpen) {
      document.addEventListener('keydown', handleKeyPress)
      return () => document.removeEventListener('keydown', handleKeyPress)
    }
  }, [isOpen, isFullscreen]) // eslint-disable-line react-hooks/exhaustive-deps

  const goToNextSlide = () => {
    setCurrentSlide(prev => (prev + 1) % slides.length)
  }

  const goToPrevSlide = () => {
    setCurrentSlide(prev => (prev - 1 + slides.length) % slides.length)
  }

  const togglePlayPause = () => {
    setIsPlaying(!isPlaying)
  }

  const resetSlideshow = () => {
    setCurrentSlide(0)
    setIsPlaying(false)
  }

  const toggleFullscreen = () => {
    if (!isFullscreen) {
      if (containerRef.current?.requestFullscreen) {
        containerRef.current.requestFullscreen()
        setIsFullscreen(true)
      }
    } else {
      exitFullscreen()
    }
  }

  const exitFullscreen = () => {
    if (document.exitFullscreen && document.fullscreenElement) {
      document.exitFullscreen().catch(() => {
        // Ignore errors if already exited
      })
      setIsFullscreen(false)
    }
  }

  const downloadSlide = () => {
    if (slides[currentSlide]) {
      const blob = new Blob([slides[currentSlide]], { type: 'text/html' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `slide-${currentSlide + 1}.html`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
    }
  }

  const shareSlide = async () => {
    if (navigator.share && slides[currentSlide]) {
      try {
        await navigator.share({
          title: title || 'عرض تقديمي',
          text: 'شاهد هذا العرض التقديمي',
          url: window.location.href
        })
      } catch (error) {
        console.log('Error sharing:', error)
      }
    }
  }

  if (!isOpen) return null

  return (
    <AnimatePresence>
      <motion.div
        ref={containerRef}
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className={`fixed inset-0 z-50 bg-black/90 backdrop-blur-sm ${
          isFullscreen ? 'bg-black' : ''
        }`}
        onClick={(e) => {
          if (e.target === e.currentTarget && !isFullscreen) {
            onClose()
          }
        }}
      >
        <div className="h-full flex flex-col">
          {/* Header */}
          {!isFullscreen && (
            <motion.div
              initial={{ y: -50, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              className="flex items-center justify-between p-4 bg-black/50"
            >
              <div className="flex items-center space-x-4 rtl:space-x-reverse">
                <h2 className="text-white text-lg font-semibold">
                  {title || 'عرض تقديمي'}
                </h2>
                <span className="text-white/70 text-sm">
                  {currentSlide + 1} / {slides.length}
                </span>
              </div>
              
              <div className="flex items-center space-x-2 rtl:space-x-reverse">
                <button
                  onClick={shareSlide}
                  className="p-2 text-white/70 hover:text-white hover:bg-white/10 rounded-lg transition-colors"
                  title="مشاركة"
                >
                  <Share2 className="w-5 h-5" />
                </button>
                <button
                  onClick={downloadSlide}
                  className="p-2 text-white/70 hover:text-white hover:bg-white/10 rounded-lg transition-colors"
                  title="تحميل"
                >
                  <Download className="w-5 h-5" />
                </button>
                <button
                  onClick={toggleFullscreen}
                  className="p-2 text-white/70 hover:text-white hover:bg-white/10 rounded-lg transition-colors"
                  title="ملء الشاشة"
                >
                  <Maximize2 className="w-5 h-5" />
                </button>
                <button
                  onClick={onClose}
                  className="p-2 text-white/70 hover:text-white hover:bg-white/10 rounded-lg transition-colors"
                  title="إغلاق"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>
            </motion.div>
          )}

          {/* Main Content */}
          <div className="flex-1 flex items-center justify-center p-2 sm:p-4">
            <motion.div
              key={currentSlide}
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.9 }}
              transition={{ duration: 0.3 }}
              className="w-full max-w-6xl aspect-video bg-white rounded-lg overflow-hidden shadow-2xl"
              style={{
                maxHeight: 'calc(100vh - 200px)',
                minHeight: '300px'
              }}
            >
              {slides[currentSlide] && (
                <iframe
                  ref={iframeRef}
                  srcDoc={slides[currentSlide]}
                  className="w-full h-full border-0"
                  title={`Slide ${currentSlide + 1}`}
                  sandbox="allow-scripts allow-same-origin"
                  style={{
                    minHeight: '100%',
                    maxWidth: '100%'
                  }}
                />
              )}
            </motion.div>
          </div>

          {/* Controls */}
          <motion.div
            initial={{ y: 50, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            className="flex items-center justify-center p-4 bg-black/50"
          >
            <div className="flex items-center space-x-4 rtl:space-x-reverse bg-black/30 rounded-full px-6 py-3">
              <button
                onClick={resetSlideshow}
                className="p-2 text-white/70 hover:text-white hover:bg-white/10 rounded-full transition-colors"
                title="إعادة تشغيل"
              >
                <RotateCcw className="w-5 h-5" />
              </button>
              
              <button
                onClick={goToPrevSlide}
                disabled={slides.length <= 1}
                className="p-2 text-white/70 hover:text-white hover:bg-white/10 rounded-full transition-colors disabled:opacity-30"
                title="الشريحة السابقة"
              >
                <ChevronLeft className="w-5 h-5" />
              </button>
              
              <button
                onClick={togglePlayPause}
                disabled={slides.length <= 1}
                className="p-3 text-white bg-white/20 hover:bg-white/30 rounded-full transition-colors disabled:opacity-30"
                title={isPlaying ? 'إيقاف' : 'تشغيل'}
              >
                {isPlaying ? <Pause className="w-6 h-6" /> : <Play className="w-6 h-6" />}
              </button>
              
              <button
                onClick={goToNextSlide}
                disabled={slides.length <= 1}
                className="p-2 text-white/70 hover:text-white hover:bg-white/10 rounded-full transition-colors disabled:opacity-30"
                title="الشريحة التالية"
              >
                <ChevronRight className="w-5 h-5" />
              </button>
            </div>
          </motion.div>

          {/* Slide Indicators */}
          {slides.length > 1 && (
            <div className="flex justify-center pb-4">
              <div className="flex space-x-2 rtl:space-x-reverse">
                {slides.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => setCurrentSlide(index)}
                    className={`w-2 h-2 rounded-full transition-colors ${
                      index === currentSlide ? 'bg-white' : 'bg-white/30'
                    }`}
                  />
                ))}
              </div>
            </div>
          )}
        </div>
      </motion.div>
    </AnimatePresence>
  )
}
