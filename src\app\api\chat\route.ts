import { NextRequest, NextResponse } from 'next/server'
import { AIManager } from '@/lib/ai-manager'
import type { Message, ChatMode, Locale } from '@/types'

// Initialize AI Manager
const aiManager = new AIManager()

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { 
      messages, 
      mode = 'chat', 
      locale = 'en', 
      userId,
      stream = false 
    }: {
      messages: Message[]
      mode: ChatMode
      locale: Locale
      userId?: string
      stream?: boolean
    } = body

    // Validate input
    if (!messages || !Array.isArray(messages)) {
      return NextResponse.json(
        { error: 'Messages array is required' },
        { status: 400 }
      )
    }

    if (messages.length === 0) {
      return NextResponse.json(
        { error: 'At least one message is required' },
        { status: 400 }
      )
    }

    // Generate response
    const result = await aiManager.generateResponse(
      messages,
      mode,
      locale,
      userId,
      stream
    )

    if (stream && 'stream' in result) {
      // Return streaming response
      return new Response(result.stream, {
        headers: {
          'Content-Type': 'text/plain; charset=utf-8',
          'Transfer-Encoding': 'chunked',
          'X-Model': result.model,
          'X-Provider': result.provider,
        },
      })
    } else {
      // Return regular response
      return NextResponse.json({
        success: true,
        data: result
      })
    }

  } catch (error) {
    console.error('Chat API Error:', error)
    
    return NextResponse.json(
      { 
        success: false,
        error: error instanceof Error ? error.message : 'Internal server error' 
      },
      { status: 500 }
    )
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'WIDDX AI Chat API is running',
    version: '1.0.0',
    endpoints: {
      chat: 'POST /api/chat',
      health: 'GET /api/health'
    }
  })
}
