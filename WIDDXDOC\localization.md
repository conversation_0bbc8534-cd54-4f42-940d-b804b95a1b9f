# Localization System Documentation

## Overview
The application supports internationalization (i18n) with English and Arabic translations. The system includes:
- Translation management
- RTL/LTR direction support
- Browser language detection
- Local storage persistence

## Core Files
- `src/locales/index.ts`: Main translation logic
- `src/locales/en.ts`: English translations
- `src/locales/ar.ts`: Arabic translations  
- `src/hooks/useLocale.ts`: React locale management hook

## Translation Function (`t()`)
Usage:
```typescript
t(locale, 'app.title') // Returns localized string
t(locale, 'time.minutesAgo', { count: 5 }) // With parameters
```

Features:
- Dot notation for nested keys
- Parameter substitution with `{param}`
- Fallback to English if translation missing
- Warning for missing keys

## Adding New Languages
1. Create new locale file (e.g. `fr.ts`)
2. Add to `locales/index.ts`:
```typescript
import { fr } from './fr'
export const locales = {
  en,
  ar,
  fr
}
```

3. Update `Locale` type in `@/types`

## RTL Support
Arabic automatically triggers:
- `dir="rtl"` on HTML element
- RTL CSS classes when needed

## useLocale Hook
Manages:
- Current locale state
- LocalStorage persistence
- Browser language detection
- Document direction/lang attributes

Usage:
```typescript
const { locale, setLocale } = useLocale()
```

## Translation Key Conventions
- Grouped by feature area (app, chat, settings, etc.)
- Consistent naming (camelCase)
- Parameterized strings for dynamic content
- Comprehensive coverage (239 strings per language)

## Best Practices
1. Always use translation keys in components
2. Add new strings to both language files
3. Test RTL layout for Arabic
4. Verify parameter substitution
5. Check console for missing key warnings