/**
 * MCP Integration Service - Specialized service for integrating MCP capabilities
 * into the WIDDX AI presentation system
 */

import { mcpServiceManager, type MCPResponse, type SearchResult, type ChartConfig } from './mcp-service-manager'

interface EnhancedPresentationData {
  topic: string;
  researchData?: any;
  factCheckData?: any;
  visualAssets?: any;
  timeData?: any;
  mcpServices: string[];
}

interface ContentEnhancement {
  originalContent: string;
  enhancedContent: string;
  sources: SearchResult[];
  confidence: number;
  mcpServices: string[];
}

class MCPIntegrationService {
  
  /**
   * Enhance presentation content with comprehensive MCP services
   */
  async enhancePresentationContent(topic: string, existingContent?: any): Promise<EnhancedPresentationData> {
    console.log('🚀 Starting comprehensive MCP enhancement for:', topic);

    const [
      researchData,
      factCheckData,
      visualAssets,
      timeData
    ] = await Promise.all([
      this.gatherResearchData(topic),
      this.performFactChecking(topic),
      this.generateVisualAssets(topic),
      this.getTimeData()
    ]);

    const mcpServices = this.extractMCPServices([researchData, factCheckData, visualAssets, timeData]);

    console.log('✅ MCP enhancement completed with services:', mcpServices);

    return {
      topic,
      researchData,
      factCheckData,
      visualAssets,
      timeData,
      mcpServices
    };
  }

  /**
   * Enhance specific content with fact-checking and research
   */
  async enhanceContentWithResearch(content: string, topic: string): Promise<ContentEnhancement> {
    try {
      // Search for supporting information
      const researchResponse = await mcpServiceManager.searchWeb(`${topic} ${content}`, {
        count: 3,
        safesearch: 'moderate'
      });

      // Fact-check the content
      const factCheckResponse = await mcpServiceManager.searchWeb(`fact check ${content}`, {
        count: 2,
        safesearch: 'strict'
      });

      let enhancedContent = content;
      let sources: SearchResult[] = [];
      let confidence = 0.5;
      let mcpServices: string[] = [];

      if (researchResponse.success) {
        sources = [...sources, ...researchResponse.data];
        mcpServices.push(researchResponse.source);
        
        // Enhance content with research insights
        const insights = this.extractInsights(researchResponse.data);
        enhancedContent = this.integrateInsights(content, insights);
        confidence += 0.3;
      }

      if (factCheckResponse.success) {
        sources = [...sources, ...factCheckResponse.data];
        mcpServices.push(factCheckResponse.source);
        confidence += 0.2;
      }

      return {
        originalContent: content,
        enhancedContent,
        sources,
        confidence: Math.min(confidence, 1.0),
        mcpServices: [...new Set(mcpServices)]
      };
    } catch (error) {
      console.error('❌ Content enhancement error:', error);
      return {
        originalContent: content,
        enhancedContent: content,
        sources: [],
        confidence: 0.5,
        mcpServices: []
      };
    }
  }

  /**
   * Generate data visualizations using MCP services
   */
  async generateDataVisualization(data: any, title: string): Promise<MCPResponse> {
    try {
      const chartConfig: ChartConfig = {
        type: this.determineChartType(data),
        data: this.formatChartData(data, title),
        options: {
          responsive: true,
          plugins: {
            title: {
              display: true,
              text: title
            },
            legend: {
              position: 'top'
            }
          }
        }
      };

      return await mcpServiceManager.generateChart(chartConfig);
    } catch (error) {
      console.error('❌ Data visualization error:', error);
      return {
        success: false,
        error: 'Failed to generate data visualization',
        source: 'mcp-integration'
      };
    }
  }

  /**
   * Get localized time and date information
   */
  async getLocalizedTimeInfo(locale: string = 'ar-SA', timezone: string = 'Asia/Riyadh'): Promise<MCPResponse> {
    return await mcpServiceManager.formatTime({
      locale,
      timezone,
      timestamp: Date.now()
    });
  }

  /**
   * Fetch and process external content
   */
  async fetchExternalContent(url: string): Promise<MCPResponse> {
    return await mcpServiceManager.fetchWebContent(url);
  }

  /**
   * Get MCP service status and health
   */
  getMCPServiceStatus(): any {
    return mcpServiceManager.getServiceStatus();
  }

  /**
   * Clear MCP cache
   */
  clearMCPCache(): void {
    mcpServiceManager.clearCache();
  }

  // Private helper methods

  private async gatherResearchData(topic: string): Promise<any> {
    const searchResponse = await mcpServiceManager.searchWeb(topic, {
      count: 5,
      market: 'en-US'
    });

    return searchResponse.success ? {
      data: searchResponse.data,
      source: searchResponse.source,
      timestamp: new Date().toISOString()
    } : null;
  }

  private async performFactChecking(topic: string): Promise<any> {
    const factCheckResponse = await mcpServiceManager.searchWeb(`${topic} facts statistics research`, {
      count: 3,
      safesearch: 'strict'
    });

    return factCheckResponse.success ? {
      data: factCheckResponse.data,
      source: factCheckResponse.source,
      confidence: 0.85,
      timestamp: new Date().toISOString()
    } : null;
  }

  private async generateVisualAssets(topic: string): Promise<any> {
    // For now, return placeholder data
    // In production, this would integrate with image generation MCP services
    return {
      placeholder: true,
      topic,
      assets: [],
      source: 'placeholder'
    };
  }

  private async getTimeData(): Promise<any> {
    const timeResponse = await mcpServiceManager.formatTime({
      locale: 'ar-SA',
      timezone: 'Asia/Riyadh'
    });

    return timeResponse.success ? timeResponse.data : null;
  }

  private extractMCPServices(responses: any[]): string[] {
    const services: string[] = [];
    responses.forEach(response => {
      if (response && response.source) {
        services.push(response.source);
      }
    });
    return [...new Set(services)];
  }

  private extractInsights(searchResults: SearchResult[]): string[] {
    return searchResults.map(result => result.snippet).slice(0, 3);
  }

  private integrateInsights(content: string, insights: string[]): string {
    if (insights.length === 0) return content;
    
    const additionalInfo = insights.join(' ');
    return `${content}\n\nمعلومات إضافية: ${additionalInfo}`;
  }

  private determineChartType(data: any): 'bar' | 'line' | 'pie' | 'doughnut' | 'scatter' {
    if (Array.isArray(data) && data.length <= 5) return 'pie';
    if (Array.isArray(data) && data.length > 5) return 'bar';
    return 'bar';
  }

  private formatChartData(data: any, title: string): any {
    if (Array.isArray(data)) {
      return {
        labels: data.map((_, index) => `Item ${index + 1}`),
        datasets: [{
          label: title,
          data: data,
          backgroundColor: [
            '#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6'
          ]
        }]
      };
    }
    
    return {
      labels: ['Data'],
      datasets: [{
        label: title,
        data: [1],
        backgroundColor: ['#3B82F6']
      }]
    };
  }
}

// Export singleton instance
export const mcpIntegrationService = new MCPIntegrationService();
export type { EnhancedPresentationData, ContentEnhancement };
