import type { Message } from '@/types'

export interface DeepSeekMessage {
  role: 'system' | 'user' | 'assistant'
  content: string
}

export interface DeepSeekResponse {
  id: string
  object: string
  created: number
  model: string
  choices: {
    index: number
    message: {
      role: string
      content: string
    }
    finish_reason: string
  }[]
  usage: {
    prompt_tokens: number
    completion_tokens: number
    total_tokens: number
  }
}

export class DeepSeekService {
  private apiKey: string
  private baseUrl: string

  constructor() {
    this.apiKey = process.env.DEEPSEEK_API_KEY || ''
    this.baseUrl = process.env.DEEPSEEK_API_URL || 'https://api.deepseek.com/v1'
    
    if (!this.apiKey) {
      console.warn('DEEPSEEK_API_KEY is not configured. DeepSeekService will operate in no-op mode.')
      // Do not throw, allow fallback to Gemini
    }
  }

  async generateResponse(
    messages: Message[],
    systemPrompt: string,
    model: string = 'deepseek-chat',
    stream: boolean = false
  ): Promise<string | ReadableStream> {
    if (!this.apiKey) {
      throw new Error('DeepSeek API key is not configured')
    }
    const deepseekMessages: DeepSeekMessage[] = [
      { role: 'system', content: systemPrompt },
      ...messages.map(msg => ({
        role: msg.role as 'user' | 'assistant',
        content: msg.content
      }))
    ]

    const requestBody = {
      model,
      messages: deepseekMessages,
      max_tokens: parseInt(process.env.MAX_TOKENS || '4000'),
      temperature: parseFloat(process.env.TEMPERATURE || '0.7'),
      stream,
      top_p: 0.95,
      frequency_penalty: 0,
      presence_penalty: 0
    }

    try {
      const response = await fetch(`${this.baseUrl}/chat/completions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`,
          'User-Agent': 'WIDDX-AI/1.0'
        },
        body: JSON.stringify(requestBody)
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(`DeepSeek API error: ${response.status} - ${errorData.error?.message || 'Unknown error'}`)
      }

      if (stream) {
        return this.handleStreamResponse(response)
      } else {
        const data: DeepSeekResponse = await response.json()
        return data.choices[0]?.message?.content || 'No response generated'
      }
    } catch (error) {
      console.error('DeepSeek API Error:', error)
      throw new Error(`Failed to generate response: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  private async handleStreamResponse(response: Response): Promise<ReadableStream> {
    const reader = response.body?.getReader()
    if (!reader) {
      throw new Error('No response body available')
    }

    return new ReadableStream({
      async start(controller) {
        const decoder = new TextDecoder()
        
        try {
          while (true) {
            const { done, value } = await reader.read()
            
            if (done) {
              controller.close()
              break
            }

            const chunk = decoder.decode(value, { stream: true })
            const lines = chunk.split('\n')

            for (const line of lines) {
              if (line.startsWith('data: ')) {
                const data = line.slice(6).trim()
                
                if (data === '[DONE]') {
                  controller.close()
                  return
                }

                try {
                  const parsed = JSON.parse(data)
                  const content = parsed.choices?.[0]?.delta?.content
                  
                  if (content) {
                    controller.enqueue(new TextEncoder().encode(content))
                  }
                } catch (e) {
                  // Skip invalid JSON
                  continue
                }
              }
            }
          }
        } catch (error) {
          controller.error(error)
        }
      }
    })
  }

  async checkHealth(): Promise<boolean> {
    if (!this.apiKey) {
      return false
    }

    try {
      // Test with a simple completion request instead of models endpoint
      const testResponse = await fetch(`${this.baseUrl}/chat/completions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`,
          'User-Agent': 'WIDDX-AI/1.0'
        },
        body: JSON.stringify({
          model: 'deepseek-chat',
          messages: [{ role: 'user', content: 'test' }],
          max_tokens: 1
        })
      })
      return testResponse.ok || testResponse.status === 429 // 429 means rate limited but API is working
    } catch {
      return false
    }
  }
}