export const ar = {
  // App metadata
  app: {
    title: 'تحدث مع Widdx.ai- روبوت محادثة ذكي مجاني مدعوم بـ GLM-4.5',
    description: 'ابدأ محادثة مجانية مع خبير الذكاء الاصطناعي للبرمجة والأدوات الذكية. أخبر Widdx.aiبما تحتاجه - تطبيق ويب كامل، عرض تقديمي مذهل، أو كتابة احترافية - واحصل على نتائج فورية.',
  },

  // Navigation and UI
  nav: {
    newChat: 'محادثة جديدة',
    chatHistory: 'سجل المحادثات',
    settings: 'الإعدادات',
    help: 'المساعدة',
    about: 'حول',
  },

  // Chat modes
  modes: {
    chat: {
      name: 'محادثة',
      description: 'محادثة عامة مع الذكاء الاصطناعي',
      placeholder: 'اسأل WIDDX AI أي شيء...',
    },
    slides: {
      name: 'عروض تقديمية ذكية',
      description: 'إنشاء عروض تقديمية مذهلة',
      placeholder: 'صف موضوع العرض التقديمي...',
    },
    code: {
      name: 'مساعد البرمجة',
      description: 'مساعدة في البرمجة وتوليد الكود',
      placeholder: 'صف مهمة البرمجة...',
    },
    summarizer: {
      name: 'ملخص',
      description: 'تلخيص النصوص والوثائق',
      placeholder: 'الصق النص للتلخيص...',
    },
    writer: {
      name: 'كاتب',
      description: 'مساعدة في الكتابة الاحترافية',
      placeholder: 'ماذا تريد أن تكتب؟',
    },
    research: {
      name: 'بحث',
      description: 'تحليل عميق ورؤى ذكية',
      placeholder: 'ماذا تريد أن تبحث؟',
    },
  },

  // Welcome screen
  welcome: {
    title: 'مرحباً بك في WIDDX AI',
    subtitle: 'واجهة دردشة ذكية متقدمة',
    features: {
      code: {
        title: 'توليد الكود',
        description: 'إنشاء تطبيقات كاملة وإصلاح الأخطاء البرمجية',
      },
      slides: {
        title: 'العروض التقديمية',
        description: 'إنشاء عروض تقديمية مذهلة وشرائح احترافية',
      },
      writing: {
        title: 'الكتابة',
        description: 'كتابة احترافية وإنشاء محتوى عالي الجودة',
      },
      research: {
        title: 'البحث',
        description: 'تحليل عميق ورؤى ذكية',
      },
    },
    tryAsking: 'جرب أن تسأل:',
    suggestions: [
      'أنشئ صفحة هبوط لشركة تقنية ناشئة',
      'ساعدني في إصلاح هذا الكود البرمجي',
      'اكتب اقتراح عمل لتطبيق جديد',
      'اشرح الحوسبة الكمية بطريقة بسيطة',
      'أنشئ عرض تقديمي عن اتجاهات الذكاء الاصطناعي',
      'لخص هذا البحث العلمي',
    ],
  },

  // Chat interface
  chat: {
    inputPlaceholder: 'أرسل رسالة...',
    send: 'إرسال',
    regenerate: 'إعادة توليد',
    copy: 'نسخ',
    edit: 'تعديل',
    delete: 'حذف',
    typing: 'WIDDX AI يكتب...',
    characterCount: '{count}/{max}',
    disclaimer: 'قد يرتكب WIDDX AI أخطاء. فكر في التحقق من المعلومات المهمة.',
    emptyState: 'لا توجد رسائل بعد. ابدأ محادثة!',
  },

  // Sidebar
  sidebar: {
    title: 'سجل المحادثات',
    noChats: 'لا توجد محادثات بعد',
    deleteChat: 'حذف المحادثة',
    confirmDelete: 'هل أنت متأكد من حذف هذه المحادثة؟',
    today: 'اليوم',
    yesterday: 'أمس',
    lastWeek: 'آخر 7 أيام',
    lastMonth: 'آخر 30 يوماً',
    older: 'أقدم',
  },

  // Settings
  settings: {
    theme: 'المظهر',
    language: 'اللغة',
    model: 'نموذج الذكاء الاصطناعي',
    temperature: 'الإبداع',
    maxTokens: 'طول الاستجابة',
    light: 'فاتح',
    dark: 'داكن',
    system: 'النظام',
    english: 'English',
    arabic: 'العربية',
  },

  // Error messages
  errors: {
    generic: 'حدث خطأ ما. يرجى المحاولة مرة أخرى.',
    network: 'خطأ في الشبكة. يرجى التحقق من اتصالك.',
    rateLimit: 'طلبات كثيرة جداً. يرجى الانتظار قليلاً.',
    invalidInput: 'إدخال غير صحيح. يرجى التحقق من رسالتك.',
    serverError: 'خطأ في الخادم. يرجى المحاولة لاحقاً.',
    unauthorized: 'غير مصرح. يرجى تحديث الصفحة.',
    notFound: 'المورد غير موجود.',
    timeout: 'انتهت مهلة الطلب. يرجى المحاولة مرة أخرى.',
  },

  // Loading states
  loading: {
    initializing: 'تهيئة WIDDX AI...',
    generating: 'توليد الاستجابة...',
    thinking: 'يفكر...',
    processing: 'معالجة طلبك...',
    saving: 'حفظ...',
    loading: 'تحميل...',
  },

  // Actions
  actions: {
    retry: 'إعادة المحاولة',
    cancel: 'إلغاء',
    confirm: 'تأكيد',
    save: 'حفظ',
    reset: 'إعادة تعيين',
    clear: 'مسح',
    export: 'تصدير',
    import: 'استيراد',
    share: 'مشاركة',
    download: 'تحميل',
  },

  // Keyboard shortcuts
  shortcuts: {
    newChat: 'محادثة جديدة',
    send: 'إرسال رسالة',
    toggleSidebar: 'تبديل الشريط الجانبي',
    toggleTheme: 'تبديل المظهر',
    focusInput: 'التركيز على الإدخال',
    clearChat: 'مسح المحادثة',
  },

  // Accessibility
  a11y: {
    toggleTheme: 'التبديل بين المظهر الفاتح والداكن',
    toggleSidebar: 'تبديل رؤية الشريط الجانبي',
    sendMessage: 'إرسال رسالة',
    newChat: 'بدء محادثة جديدة',
    deleteChat: 'حذف هذه المحادثة',
    copyMessage: 'نسخ الرسالة إلى الحافظة',
    regenerateResponse: 'إعادة توليد استجابة الذكاء الاصطناعي',
    editMessage: 'تعديل هذه الرسالة',
    scrollToBottom: 'التمرير إلى أسفل المحادثة',
    openSettings: 'فتح الإعدادات',
    closeModal: 'إغلاق النافذة المنبثقة',
    selectMode: 'اختيار وضع المحادثة',
    toggleLanguage: 'تبديل اللغة',
  },

  // Time formatting
  time: {
    now: 'الآن',
    minuteAgo: 'منذ دقيقة',
    minutesAgo: 'منذ {count} دقائق',
    hourAgo: 'منذ ساعة',
    hoursAgo: 'منذ {count} ساعات',
    dayAgo: 'منذ يوم',
    daysAgo: 'منذ {count} أيام',
    weekAgo: 'منذ أسبوع',
    weeksAgo: 'منذ {count} أسابيع',
    monthAgo: 'منذ شهر',
    monthsAgo: 'منذ {count} أشهر',
    yearAgo: 'منذ سنة',
    yearsAgo: 'منذ {count} سنوات',
  },

  // File handling
  files: {
    upload: 'رفع ملف',
    dragDrop: 'اسحب وأفلت الملفات هنا',
    maxSize: 'الحد الأقصى لحجم الملف: {size} ميجابايت',
    supportedFormats: 'التنسيقات المدعومة: {formats}',
    processing: 'معالجة الملف...',
    error: 'خطأ في معالجة الملف',
  },

  // Models
  models: {
    'glm-4.5': {
      name: 'GLM-4.5',
      description: 'أحدث وأقوى نموذج',
    },
    'glm-4': {
      name: 'GLM-4',
      description: 'أد��ء متوازن وسرعة',
    },
    'glm-3.5': {
      name: 'GLM-3.5',
      description: 'سريع وفعال',
    },
  },

  // Footer
  footer: {
    poweredBy: 'مدعوم بواسطة',
    version: 'الإصدار {version}',
    privacy: 'سياسة الخصوصية',
    terms: 'شروط الخدمة',
    contact: 'اتصل بنا',
  },
}