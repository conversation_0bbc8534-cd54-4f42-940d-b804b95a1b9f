'use client'

import React, { useState, useEffect, useRef } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  X, 
  ChevronLeft, 
  ChevronRight, 
  Play, 
  Pause, 
  RotateCcw, 
  Maximize, 
  Download,
  Share2,
  Settings,
  Timer,
  Grid3X3
} from 'lucide-react'

interface EnhancedSlideViewerProps {
  isOpen: boolean
  onClose: () => void
  content: string
  title: string
}

export default function EnhancedSlideViewer({ 
  isOpen, 
  onClose, 
  content, 
  title 
}: EnhancedSlideViewerProps) {
  const [currentSlide, setCurrentSlide] = useState(0)
  const [totalSlides, setTotalSlides] = useState(0)
  const [isPlaying, setIsPlaying] = useState(false)
  const [autoAdvanceTime, setAutoAdvanceTime] = useState(5)
  const [showControls, setShowControls] = useState(true)
  const [showSettings, setShowSettings] = useState(false)
  const [showThumbnails, setShowThumbnails] = useState(false)
  const [isFullscreen, setIsFullscreen] = useState(false)
  
  const iframeRef = useRef<HTMLIFrameElement>(null)
  const autoAdvanceRef = useRef<NodeJS.Timeout>()
  const controlsTimeoutRef = useRef<NodeJS.Timeout>()

  // Extract slides from content
  useEffect(() => {
    if (content) {
      const slideMatches = content.match(/<div[^>]*class[^>]*slide[^>]*>/gi) || []
      setTotalSlides(slideMatches.length)
      setCurrentSlide(0)
    }
  }, [content])

  // Auto-advance functionality
  useEffect(() => {
    if (isPlaying && totalSlides > 1) {
      autoAdvanceRef.current = setInterval(() => {
        setCurrentSlide(prev => {
          if (prev >= totalSlides - 1) {
            setIsPlaying(false)
            return prev
          }
          return prev + 1
        })
      }, autoAdvanceTime * 1000)
    } else {
      if (autoAdvanceRef.current) {
        clearInterval(autoAdvanceRef.current)
      }
    }

    return () => {
      if (autoAdvanceRef.current) {
        clearInterval(autoAdvanceRef.current)
      }
    }
  }, [isPlaying, autoAdvanceTime, totalSlides])

  // Auto-hide controls
  useEffect(() => {
    if (showControls) {
      if (controlsTimeoutRef.current) {
        clearTimeout(controlsTimeoutRef.current)
      }
      controlsTimeoutRef.current = setTimeout(() => {
        setShowControls(false)
      }, 3000)
    }

    return () => {
      if (controlsTimeoutRef.current) {
        clearTimeout(controlsTimeoutRef.current)
      }
    }
  }, [showControls])

  // Keyboard navigation
  useEffect(() => {
    const handleKeyPress = (e: KeyboardEvent) => {
      if (!isOpen) return

      switch (e.key) {
        case 'ArrowRight':
        case ' ':
          e.preventDefault()
          nextSlide()
          break
        case 'ArrowLeft':
          e.preventDefault()
          prevSlide()
          break
        case 'Home':
          e.preventDefault()
          setCurrentSlide(0)
          break
        case 'End':
          e.preventDefault()
          setCurrentSlide(totalSlides - 1)
          break
        case 'p':
        case 'P':
          e.preventDefault()
          togglePlayPause()
          break
        case 'f':
        case 'F11':
          e.preventDefault()
          toggleFullscreen()
          break
        case 'Escape':
          if (isFullscreen) {
            exitFullscreen()
          } else {
            onClose()
          }
          break
      }
    }

    document.addEventListener('keydown', handleKeyPress)
    return () => document.removeEventListener('keydown', handleKeyPress)
  }, [isOpen, currentSlide, totalSlides, isFullscreen]) // eslint-disable-line react-hooks/exhaustive-deps

  const nextSlide = () => {
    if (currentSlide < totalSlides - 1) {
      setCurrentSlide(prev => prev + 1)
      setShowControls(true)
    }
  }

  const prevSlide = () => {
    if (currentSlide > 0) {
      setCurrentSlide(prev => prev - 1)
      setShowControls(true)
    }
  }

  const togglePlayPause = () => {
    setIsPlaying(!isPlaying)
    setShowControls(true)
  }

  const resetPresentation = () => {
    setCurrentSlide(0)
    setIsPlaying(false)
    setShowControls(true)
  }

  const toggleFullscreen = () => {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen()
      setIsFullscreen(true)
    } else {
      document.exitFullscreen()
      setIsFullscreen(false)
    }
  }

  const exitFullscreen = () => {
    if (document.fullscreenElement) {
      document.exitFullscreen()
    }
    setIsFullscreen(false)
  }

  const downloadPresentation = () => {
    const blob = new Blob([content], { type: 'text/html' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `${title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.html`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  const sharePresentation = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: title,
          text: `Check out this presentation: ${title}`,
          url: window.location.href
        })
      } catch (err) {
        // Sharing failed, fallback to clipboard
      }
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(window.location.href)
      // You could show a toast notification here
    }
  }

  // Generate slide content with current slide highlighted
  const generateSlideContent = () => {
    if (!content) return content

    // Inject JavaScript to control slide navigation
    const scriptInjection = `
      <script>
        let currentSlideIndex = ${currentSlide};
        const slides = document.querySelectorAll('.slide');
        
        function showSlide(index) {
          slides.forEach((slide, i) => {
            slide.style.display = i === index ? 'flex' : 'none';
          });
        }
        
        // Show current slide when loaded
        document.addEventListener('DOMContentLoaded', () => {
          showSlide(currentSlideIndex);
        });
        
        // Also run immediately in case DOM is already loaded
        showSlide(currentSlideIndex);
      </script>
    `

    return content.replace('</body>', `${scriptInjection}</body>`)
  }

  if (!isOpen) return null

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black z-50 flex flex-col"
        onMouseMove={() => setShowControls(true)}
      >
        {/* Header Controls */}
        <AnimatePresence>
          {showControls && (
            <motion.div
              initial={{ y: -100, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              exit={{ y: -100, opacity: 0 }}
              className="absolute top-0 left-0 right-0 z-10 bg-gradient-to-b from-black/80 to-transparent p-4"
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <button
                    onClick={onClose}
                    className="p-2 text-white hover:bg-white/20 rounded-lg transition-colors"
                  >
                    <X className="w-5 h-5" />
                  </button>
                  <h1 className="text-white font-semibold text-lg">{title}</h1>
                </div>
                
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => setShowThumbnails(!showThumbnails)}
                    className="p-2 text-white hover:bg-white/20 rounded-lg transition-colors"
                  >
                    <Grid3X3 className="w-5 h-5" />
                  </button>
                  <button
                    onClick={() => setShowSettings(!showSettings)}
                    className="p-2 text-white hover:bg-white/20 rounded-lg transition-colors"
                  >
                    <Settings className="w-5 h-5" />
                  </button>
                  <button
                    onClick={sharePresentation}
                    className="p-2 text-white hover:bg-white/20 rounded-lg transition-colors"
                  >
                    <Share2 className="w-5 h-5" />
                  </button>
                  <button
                    onClick={downloadPresentation}
                    className="p-2 text-white hover:bg-white/20 rounded-lg transition-colors"
                  >
                    <Download className="w-5 h-5" />
                  </button>
                  <button
                    onClick={toggleFullscreen}
                    className="p-2 text-white hover:bg-white/20 rounded-lg transition-colors"
                  >
                    <Maximize className="w-5 h-5" />
                  </button>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Main Content */}
        <div className="flex-1 relative overflow-hidden">
          <iframe
            ref={iframeRef}
            srcDoc={generateSlideContent()}
            className="w-full h-full border-0"
            title="Presentation Viewer"
            sandbox="allow-scripts allow-same-origin"
            style={{
              minHeight: '100%',
              maxWidth: '100%',
              objectFit: 'contain'
            }}
          />
        </div>

        {/* Bottom Controls */}
        <AnimatePresence>
          {showControls && (
            <motion.div
              initial={{ y: 100, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              exit={{ y: 100, opacity: 0 }}
              className="absolute bottom-0 left-0 right-0 z-10 bg-gradient-to-t from-black/80 to-transparent p-4"
            >
              <div className="flex items-center justify-center space-x-4">
                <button
                  onClick={resetPresentation}
                  className="p-2 text-white hover:bg-white/20 rounded-lg transition-colors"
                >
                  <RotateCcw className="w-5 h-5" />
                </button>
                
                <button
                  onClick={prevSlide}
                  disabled={currentSlide === 0}
                  className="p-2 text-white hover:bg-white/20 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <ChevronLeft className="w-5 h-5" />
                </button>
                
                <button
                  onClick={togglePlayPause}
                  className="p-3 bg-white/20 text-white hover:bg-white/30 rounded-full transition-colors"
                >
                  {isPlaying ? <Pause className="w-6 h-6" /> : <Play className="w-6 h-6" />}
                </button>
                
                <button
                  onClick={nextSlide}
                  disabled={currentSlide === totalSlides - 1}
                  className="p-2 text-white hover:bg-white/20 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <ChevronRight className="w-5 h-5" />
                </button>
                
                <div className="flex items-center space-x-2 text-white">
                  <Timer className="w-4 h-4" />
                  <span className="text-sm">
                    {currentSlide + 1} / {totalSlides}
                  </span>
                </div>
              </div>
              
              {/* Progress Bar */}
              <div className="mt-4 w-full bg-white/20 rounded-full h-1">
                <div
                  className="bg-white rounded-full h-1 transition-all duration-300"
                  style={{ width: `${((currentSlide + 1) / totalSlides) * 100}%` }}
                />
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Settings Panel */}
        <AnimatePresence>
          {showSettings && (
            <motion.div
              initial={{ x: 300, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              exit={{ x: 300, opacity: 0 }}
              className="absolute top-0 right-0 bottom-0 w-80 bg-black/90 backdrop-blur-sm p-6 z-20"
            >
              <h3 className="text-white font-semibold text-lg mb-4">Presentation Settings</h3>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-white text-sm mb-2">
                    Auto-advance time (seconds)
                  </label>
                  <input
                    type="range"
                    min="1"
                    max="30"
                    value={autoAdvanceTime}
                    onChange={(e) => setAutoAdvanceTime(Number(e.target.value))}
                    className="w-full"
                  />
                  <span className="text-white text-sm">{autoAdvanceTime}s</span>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>
    </AnimatePresence>
  )
}
