export const en = {
  // App metadata
  app: {
    title: 'WIDDX AI - Advanced AI Chat Interface',
    description: 'Experience the power of WIDDX AI - your intelligent assistant for coding, writing, presentations, and creative tasks. Get instant, professional-grade results with our advanced AI technology.',
  },

  // Navigation and UI
  nav: {
    newChat: 'New Chat',
    chatHistory: 'Chat History',
    settings: 'Settings',
    help: 'Help',
    about: 'About',
  },

  // Chat modes
  modes: {
    chat: {
      name: 'Chat',
      description: 'General conversation with AI',
      placeholder: 'Ask WIDDX AI anything...',
    },
    slides: {
      name: 'AI Slides',
      description: 'Create stunning presentations',
      placeholder: 'Describe your presentation topic...',
    },
    code: {
      name: 'Code Assistant',
      description: 'Programming help and code generation',
      placeholder: 'Describe your coding task...',
    },
    summarizer: {
      name: 'Summarizer',
      description: 'Summarize text and documents',
      placeholder: 'Paste text to summarize...',
    },
    writer: {
      name: 'Writer',
      description: 'Professional writing assistance',
      placeholder: 'What would you like to write?',
    },
    research: {
      name: 'Research',
      description: 'Deep analysis and insights',
      placeholder: 'What would you like to research?',
    },
  },

  // Welcome screen
  welcome: {
    title: 'Welcome to WIDDX AI',
    subtitle: 'Advanced AI Chat Interface',
    features: {
      code: {
        title: 'Code Generation',
        description: 'Generate complete applications and debug code',
      },
      slides: {
        title: 'Presentations',
        description: 'Create stunning presentations and slide decks',
      },
      writing: {
        title: 'Writing',
        description: 'Professional-grade writing and content creation',
      },
      research: {
        title: 'Research',
        description: 'Deep analysis and intelligent insights',
      },
    },
    tryAsking: 'Try asking:',
    suggestions: [
      'Create a landing page for a tech startup',
      'Help me debug this JavaScript code',
      'Write a business proposal for a new app',
      'Explain quantum computing in simple terms',
      'Generate a presentation about AI trends',
      'Summarize this research paper',
    ],
  },

  // Chat interface
  chat: {
    inputPlaceholder: 'Send a message...',
    send: 'Send',
    regenerate: 'Regenerate',
    copy: 'Copy',
    edit: 'Edit',
    delete: 'Delete',
    typing: 'Widdx.aiis typing...',
    characterCount: '{count}/{max}',
    disclaimer: 'Widdx.aican make mistakes. Consider checking important information.',
    emptyState: 'No messages yet. Start a conversation!',
  },

  // Sidebar
  sidebar: {
    title: 'Chat History',
    noChats: 'No chats yet',
    deleteChat: 'Delete chat',
    confirmDelete: 'Are you sure you want to delete this chat?',
    today: 'Today',
    yesterday: 'Yesterday',
    lastWeek: 'Last 7 days',
    lastMonth: 'Last 30 days',
    older: 'Older',
  },

  // Settings
  settings: {
    theme: 'Theme',
    language: 'Language',
    model: 'AI Model',
    temperature: 'Creativity',
    maxTokens: 'Response Length',
    light: 'Light',
    dark: 'Dark',
    system: 'System',
    english: 'English',
    arabic: 'العربية',
  },

  // Error messages
  errors: {
    generic: 'Something went wrong. Please try again.',
    network: 'Network error. Please check your connection.',
    rateLimit: 'Too many requests. Please wait a moment.',
    invalidInput: 'Invalid input. Please check your message.',
    serverError: 'Server error. Please try again later.',
    unauthorized: 'Unauthorized. Please refresh the page.',
    notFound: 'Resource not found.',
    timeout: 'Request timeout. Please try again.',
  },

  // Loading states
  loading: {
    initializing: 'Initializing Z.ai...',
    generating: 'Generating response...',
    thinking: 'Thinking...',
    processing: 'Processing your request...',
    saving: 'Saving...',
    loading: 'Loading...',
  },

  // Actions
  actions: {
    retry: 'Retry',
    cancel: 'Cancel',
    confirm: 'Confirm',
    save: 'Save',
    reset: 'Reset',
    clear: 'Clear',
    export: 'Export',
    import: 'Import',
    share: 'Share',
    download: 'Download',
  },

  // Keyboard shortcuts
  shortcuts: {
    newChat: 'New chat',
    send: 'Send message',
    toggleSidebar: 'Toggle sidebar',
    toggleTheme: 'Toggle theme',
    focusInput: 'Focus input',
    clearChat: 'Clear chat',
  },

  // Accessibility
  a11y: {
    toggleTheme: 'Toggle between light and dark theme',
    toggleSidebar: 'Toggle sidebar visibility',
    sendMessage: 'Send message',
    newChat: 'Start a new chat',
    deleteChat: 'Delete this chat',
    copyMessage: 'Copy message to clipboard',
    regenerateResponse: 'Regenerate AI response',
    editMessage: 'Edit this message',
    scrollToBottom: 'Scroll to bottom of chat',
    openSettings: 'Open settings',
    closeModal: 'Close modal',
    selectMode: 'Select chat mode',
    toggleLanguage: 'Toggle language',
  },

  // Time formatting
  time: {
    now: 'now',
    minuteAgo: '1 minute ago',
    minutesAgo: '{count} minutes ago',
    hourAgo: '1 hour ago',
    hoursAgo: '{count} hours ago',
    dayAgo: '1 day ago',
    daysAgo: '{count} days ago',
    weekAgo: '1 week ago',
    weeksAgo: '{count} weeks ago',
    monthAgo: '1 month ago',
    monthsAgo: '{count} months ago',
    yearAgo: '1 year ago',
    yearsAgo: '{count} years ago',
  },

  // File handling
  files: {
    upload: 'Upload file',
    dragDrop: 'Drag and drop files here',
    maxSize: 'Maximum file size: {size}MB',
    supportedFormats: 'Supported formats: {formats}',
    processing: 'Processing file...',
    error: 'Error processing file',
  },

  // Models
  models: {
    'glm-4.5': {
      name: 'GLM-4.5',
      description: 'Latest and most capable model',
    },
    'glm-4': {
      name: 'GLM-4',
      description: 'Balanced performance and speed',
    },
    'glm-3.5': {
      name: 'GLM-3.5',
      description: 'Fast and efficient',
    },
  },

  // Footer
  footer: {
    poweredBy: 'Powered by',
    version: 'Version {version}',
    privacy: 'Privacy Policy',
    terms: 'Terms of Service',
    contact: 'Contact Us',
  },
}