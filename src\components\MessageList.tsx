'use client'

import { motion, AnimatePresence } from 'framer-motion'
import { Message } from './Message'
import type { Message as MessageType, Locale } from '@/types'

interface MessageListProps {
  messages: MessageType[]
  onRegenerateResponse: () => Promise<void>
  onOpenSlidePanel?: (content: string, title?: string) => void
  locale: Locale
}

export function MessageList({
  messages,
  onRegenerateResponse,
  onOpenSlidePanel,
  locale,
}: MessageListProps) {
  return (
    <div className="space-y-6">
      <AnimatePresence initial={false}>
        {messages.map((message, index) => (
          <motion.div
            key={message.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{
              duration: 0.3,
              delay: index * 0.05,
              ease: 'easeOut',
            }}
            layout
          >
            <Message
              message={message}
              isLast={index === messages.length - 1}
              onRegenerate={message.role === 'assistant' && index === messages.length - 1 ? onRegenerateResponse : undefined}
              onOpenSlidePanel={onOpenSlidePanel}
              locale={locale}
            />
          </motion.div>
        ))}
      </AnimatePresence>
    </div>
  )
}