'use client'

import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'

interface ThinkingStep {
  id: string
  title: string
  description: string
  status: 'pending' | 'active' | 'completed'
  details?: string[]
  progress?: number
}

interface ThinkingProcessProps {
  topic: string
  onComplete: (result: any) => void
  isVisible: boolean
}

const ThinkingProcess: React.FC<ThinkingProcessProps> = ({ topic, onComplete, isVisible }) => {
  const [currentStep, setCurrentStep] = useState(0)
  const [steps, setSteps] = useState<ThinkingStep[]>([
    {
      id: 'analyze',
      title: '🧠 تحليل الموضوع',
      description: 'تحليل الطلب وفهم المتطلبات',
      status: 'pending',
      details: [],
      progress: 0
    },
    {
      id: 'research',
      title: '🔍 البحث والاستكشاف',
      description: 'البحث عن المعلومات والصور ذات الصلة',
      status: 'pending',
      details: [],
      progress: 0
    },
    {
      id: 'structure',
      title: '📋 تخطيط الهيكل',
      description: 'تصميم هيكل العرض التقديمي',
      status: 'pending',
      details: [],
      progress: 0
    },
    {
      id: 'design',
      title: '🎨 اختيار التصميم',
      description: 'اختيار الألوان والتخطيطات المناسبة',
      status: 'pending',
      details: [],
      progress: 0
    },
    {
      id: 'content',
      title: '✍️ إنشاء المحتوى',
      description: 'كتابة المحتوى وإضافة الرسوم البيانية',
      status: 'pending',
      details: [],
      progress: 0
    },
    {
      id: 'finalize',
      title: '✅ المراجعة والإنهاء',
      description: 'مراجعة وتحسين العرض التقديمي',
      status: 'pending',
      details: [],
      progress: 0
    }
  ])

  const [isThinking, setIsThinking] = useState(false)

  useEffect(() => {
    if (isVisible && topic) {
      startThinkingProcess()
    }
  }, [isVisible, topic])

  const startThinkingProcess = async () => {
    setIsThinking(true)
    
    // Step 1: Analyze
    await processStep(0, [
      `تحليل الموضوع: "${topic}"`,
      'تحديد نوع المحتوى المطلوب',
      'فهم السياق والجمهور المستهدف',
      'تحديد الأهداف التعليمية'
    ])

    // Step 2: Research
    await processStep(1, [
      'البحث عن معلومات موثوقة',
      'العثور على صور عالية الجودة',
      'جمع الإحصائيات والبيانات',
      'تحديد المصادر المرجعية'
    ])

    // Step 3: Structure
    await processStep(2, [
      'تصميم هيكل العرض التقديمي',
      'تحديد عدد الشرائح المطلوبة',
      'ترتيب المعلومات منطقياً',
      'اختيار أنواع الشرائح المناسبة'
    ])

    // Step 4: Design
    await processStep(3, [
      'اختيار مجموعة الألوان المناسبة',
      'تحديد الخطوط والتنسيقات',
      'اختيار التخطيطات البصرية',
      'تصميم العناصر التفاعلية'
    ])

    // Step 5: Content
    await processStep(4, [
      'كتابة المحتوى التعليمي',
      'إنشاء الرسوم البيانية',
      'إضافة الصور والعناصر البصرية',
      'تحسين التدفق والانسيابية'
    ])

    // Step 6: Finalize
    await processStep(5, [
      'مراجعة المحتوى والتصميم',
      'التأكد من الدقة والجودة',
      'تحسين التجربة التفاعلية',
      'إنهاء العرض التقديمي'
    ])

    setIsThinking(false)
    onComplete({ success: true, topic })
  }

  const processStep = async (stepIndex: number, details: string[]) => {
    // Mark step as active
    setSteps(prev => prev.map((step, index) => ({
      ...step,
      status: index === stepIndex ? 'active' : index < stepIndex ? 'completed' : 'pending'
    })))
    
    setCurrentStep(stepIndex)

    // Simulate processing with progress
    for (let i = 0; i < details.length; i++) {
      await new Promise(resolve => setTimeout(resolve, 800 + Math.random() * 1200))
      
      setSteps(prev => prev.map((step, index) => {
        if (index === stepIndex) {
          return {
            ...step,
            details: details.slice(0, i + 1),
            progress: ((i + 1) / details.length) * 100
          }
        }
        return step
      }))
    }

    // Mark step as completed
    await new Promise(resolve => setTimeout(resolve, 500))
    setSteps(prev => prev.map((step, index) => ({
      ...step,
      status: index === stepIndex ? 'completed' : step.status
    })))
  }

  if (!isVisible) return null

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.9 }}
        className="bg-white dark:bg-gray-800 rounded-2xl shadow-2xl max-w-2xl w-full max-h-[80vh] overflow-hidden"
      >
        {/* Header */}
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-6">
          <h2 className="text-2xl font-bold mb-2">🤖 WIDDX AI يفكر...</h2>
          <p className="text-blue-100">إنشاء عرض تقديمي متقدم عن: {topic}</p>
        </div>

        {/* Progress Steps */}
        <div className="p-6 max-h-[60vh] overflow-y-auto">
          <div className="space-y-4">
            {steps.map((step, index) => (
              <motion.div
                key={step.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
                className={`border rounded-lg p-4 transition-all duration-300 ${
                  step.status === 'active' 
                    ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' 
                    : step.status === 'completed'
                    ? 'border-green-500 bg-green-50 dark:bg-green-900/20'
                    : 'border-gray-200 dark:border-gray-700'
                }`}
              >
                <div className="flex items-center justify-between mb-2">
                  <h3 className={`font-semibold ${
                    step.status === 'active' ? 'text-blue-700 dark:text-blue-300' :
                    step.status === 'completed' ? 'text-green-700 dark:text-green-300' :
                    'text-gray-600 dark:text-gray-400'
                  }`}>
                    {step.title}
                  </h3>
                  
                  {step.status === 'active' && (
                    <div className="animate-spin w-5 h-5 border-2 border-blue-500 border-t-transparent rounded-full" />
                  )}
                  
                  {step.status === 'completed' && (
                    <div className="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center">
                      <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    </div>
                  )}
                </div>

                <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                  {step.description}
                </p>

                {/* Progress Bar */}
                {step.status === 'active' && step.progress !== undefined && (
                  <div className="mb-3">
                    <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                      <motion.div
                        className="bg-blue-500 h-2 rounded-full"
                        initial={{ width: 0 }}
                        animate={{ width: `${step.progress}%` }}
                        transition={{ duration: 0.5 }}
                      />
                    </div>
                  </div>
                )}

                {/* Details */}
                <AnimatePresence>
                  {step.details && step.details.length > 0 && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: 'auto' }}
                      exit={{ opacity: 0, height: 0 }}
                      className="space-y-1"
                    >
                      {step.details.map((detail, detailIndex) => (
                        <motion.div
                          key={detailIndex}
                          initial={{ opacity: 0, x: -10 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: detailIndex * 0.1 }}
                          className="text-sm text-gray-700 dark:text-gray-300 flex items-center"
                        >
                          <span className="w-2 h-2 bg-blue-400 rounded-full mr-2 flex-shrink-0" />
                          {detail}
                        </motion.div>
                      ))}
                    </motion.div>
                  )}
                </AnimatePresence>
              </motion.div>
            ))}
          </div>
        </div>

        {/* Footer */}
        <div className="bg-gray-50 dark:bg-gray-900 p-4 text-center">
          <p className="text-sm text-gray-600 dark:text-gray-400">
            {isThinking ? 'جاري العمل على إنشاء عرض تقديمي استثنائي...' : 'تم الانتهاء!'}
          </p>
        </div>
      </motion.div>
    </div>
  )
}

export default ThinkingProcess
