export interface PDFExportOptions {
  format?: 'A4' | 'Letter' | 'A3' | 'Slide'
  orientation?: 'portrait' | 'landscape'
  quality?: number
  includeCharts?: boolean
  filename?: string
}

export class PDFExporter {
  private static instance: PDFExporter
  
  static getInstance(): PDFExporter {
    if (!PDFExporter.instance) {
      PDFExporter.instance = new PDFExporter()
    }
    return PDFExporter.instance
  }

  async exportToPDF(htmlContent: string, options: PDFExportOptions = {}): Promise<Blob> {
    const defaultOptions: PDFExportOptions = {
      format: 'Slide',
      orientation: 'landscape',
      quality: 1.0,
      includeCharts: true,
      filename: 'presentation.pdf'
    }

    const finalOptions = { ...defaultOptions, ...options }

    try {
      // For now, we'll use a client-side approach with html2pdf
      // In production, you might want to use a server-side solution
      return await this.generatePDFClientSide(htmlContent, finalOptions)
    } catch (error) {
      console.error('PDF export failed:', error)
      throw new Error('Failed to export PDF. Please try again.')
    }
  }

  private async generatePDFClientSide(htmlContent: string, options: PDFExportOptions): Promise<Blob> {
    // Check if html2pdf is available, try to load it if not
    if (typeof window === 'undefined') {
      throw new Error('PDF export is not available in server-side environment.')
    }

    let html2pdf = (window as any).html2pdf

    // Try to load html2pdf if not available
    if (!html2pdf) {
      try {
        await this.loadPDFLibraries()
        html2pdf = (window as any).html2pdf
      } catch (error) {
        // Fallback to browser print dialog
        this.fallbackToPrint(htmlContent, options.filename || 'presentation')
        throw new Error('PDF export library not available. Print dialog opened as fallback.')
      }
    }

    // Create a temporary container
    const container = document.createElement('div')
    container.innerHTML = htmlContent
    container.style.position = 'absolute'
    container.style.left = '-9999px'
    container.style.top = '-9999px'
    document.body.appendChild(container)

    try {
      // Configure html2pdf options
      const pdfOptions = {
        margin: 0,
        filename: options.filename || 'presentation.pdf',
        image: { 
          type: 'jpeg', 
          quality: options.quality || 1.0 
        },
        html2canvas: { 
          scale: 2,
          useCORS: true,
          allowTaint: true,
          backgroundColor: null,
          logging: false
        },
        jsPDF: { 
          unit: 'px',
          format: this.getPageFormat(options.format || 'Slide'),
          orientation: options.orientation || 'landscape',
          compress: true
        }
      }

      // Wait for any charts to render
      if (options.includeCharts) {
        await this.waitForChartsToRender(container)
      }

      // Generate PDF
      const pdfBlob = await html2pdf()
        .set(pdfOptions)
        .from(container)
        .outputPdf('blob')

      return pdfBlob
    } finally {
      // Clean up
      document.body.removeChild(container)
    }
  }

  private getPageFormat(format: string): [number, number] {
    switch (format) {
      case 'A4':
        return [595, 842]
      case 'A3':
        return [842, 1191]
      case 'Letter':
        return [612, 792]
      case 'Slide':
      default:
        return [1280, 720]
    }
  }

  private async waitForChartsToRender(container: HTMLElement): Promise<void> {
    const charts = container.querySelectorAll('canvas')
    if (charts.length === 0) return

    // Wait for Chart.js to render
    return new Promise((resolve) => {
      let renderedCharts = 0
      const totalCharts = charts.length

      if (totalCharts === 0) {
        resolve()
        return
      }

      const checkChart = () => {
        renderedCharts++
        if (renderedCharts >= totalCharts) {
          // Give extra time for animations to complete
          setTimeout(resolve, 1000)
        }
      }

      // Set up observers for each chart
      charts.forEach((canvas) => {
        const observer = new MutationObserver(() => {
          if (canvas.width > 0 && canvas.height > 0) {
            observer.disconnect()
            checkChart()
          }
        })

        observer.observe(canvas, {
          attributes: true,
          attributeFilter: ['width', 'height']
        })

        // Fallback timeout
        setTimeout(() => {
          observer.disconnect()
          checkChart()
        }, 3000)
      })
    })
  }

  async downloadPDF(htmlContent: string, filename: string = 'presentation.pdf', options: PDFExportOptions = {}): Promise<void> {
    try {
      const pdfBlob = await this.exportToPDF(htmlContent, { ...options, filename })
      
      // Create download link
      const url = URL.createObjectURL(pdfBlob)
      const link = document.createElement('a')
      link.href = url
      link.download = filename
      link.style.display = 'none'
      
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      
      // Clean up
      URL.revokeObjectURL(url)
    } catch (error) {
      console.error('PDF download failed:', error)
      throw error
    }
  }

  // Alternative method using jsPDF directly (for simpler presentations)
  async exportToPDFSimple(htmlContent: string, options: PDFExportOptions = {}): Promise<Blob> {
    if (typeof window === 'undefined' || !(window as any).jsPDF) {
      throw new Error('jsPDF library not loaded')
    }

    const { jsPDF } = (window as any)
    const doc = new jsPDF({
      orientation: options.orientation || 'landscape',
      unit: 'px',
      format: this.getPageFormat(options.format || 'Slide')
    })

    // Parse HTML and extract text content
    const parser = new DOMParser()
    const htmlDoc = parser.parseFromString(htmlContent, 'text/html')
    
    const slides = htmlDoc.querySelectorAll('.slide')
    
    slides.forEach((slide, index) => {
      if (index > 0) {
        doc.addPage()
      }

      // Extract and add title
      const title = slide.querySelector('h1, h2, .title')
      if (title) {
        doc.setFontSize(24)
        doc.setFont('helvetica', 'bold')
        doc.text(title.textContent || '', 50, 80)
      }

      // Extract and add content
      const content = slide.querySelector('.slide-content, .feature-list')
      if (content) {
        doc.setFontSize(16)
        doc.setFont('helvetica', 'normal')
        
        const lines = this.extractTextLines(content)
        let yPosition = 120
        
        lines.forEach(line => {
          if (yPosition > 600) { // Page height limit
            doc.addPage()
            yPosition = 80
          }
          doc.text(line, 50, yPosition)
          yPosition += 25
        })
      }
    })

    return new Blob([doc.output('blob')], { type: 'application/pdf' })
  }

  private extractTextLines(element: Element): string[] {
    const lines: string[] = []
    
    const processNode = (node: Node) => {
      if (node.nodeType === Node.TEXT_NODE) {
        const text = node.textContent?.trim()
        if (text) {
          lines.push(text)
        }
      } else if (node.nodeType === Node.ELEMENT_NODE) {
        const el = node as Element
        
        if (el.tagName === 'LI') {
          const text = el.textContent?.trim()
          if (text) {
            lines.push(`• ${text}`)
          }
        } else {
          node.childNodes.forEach(processNode)
        }
      }
    }

    processNode(element)
    return lines
  }

  // Method to check if PDF export is available
  static isPDFExportAvailable(): boolean {
    return typeof window !== 'undefined' && 
           ((window as any).html2pdf || (window as any).jsPDF)
  }

  // Method to load PDF libraries dynamically
  static async loadPDFLibraries(): Promise<void> {
    if (typeof window === 'undefined') return

    const promises: Promise<void>[] = []

    // Load html2pdf
    if (!(window as any).html2pdf) {
      promises.push(
        new Promise((resolve, reject) => {
          const script = document.createElement('script')
          script.src = 'https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js'
          script.onload = () => resolve()
          script.onerror = reject
          document.head.appendChild(script)
        })
      )
    }

    // Load jsPDF as fallback
    if (!(window as any).jsPDF) {
      promises.push(
        new Promise((resolve, reject) => {
          const script = document.createElement('script')
          script.src = 'https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js'
          script.onload = () => resolve()
          script.onerror = reject
          document.head.appendChild(script)
        })
      )
    }

    await Promise.all(promises)
  }

  // Fallback method to open print dialog
  private fallbackToPrint(htmlContent: string, filename: string): void {
    const printWindow = window.open('', '_blank')
    if (printWindow) {
      printWindow.document.write(`
        <!DOCTYPE html>
        <html>
        <head>
          <title>${filename}</title>
          <style>
            body {
              margin: 0;
              padding: 20px;
              font-family: Arial, sans-serif;
              background: white;
            }
            .slide {
              page-break-after: always;
              margin-bottom: 20px;
              min-height: 90vh;
              padding: 20px;
              border: 1px solid #ddd;
            }
            .slide:last-child {
              page-break-after: avoid;
            }
            @media print {
              .slide {
                page-break-after: always;
                border: none;
                margin: 0;
                padding: 20px;
              }
              body { padding: 0; }
            }
            h1, h2 { color: #333; margin-bottom: 20px; }
            ul { margin: 10px 0; padding-left: 20px; }
            li { margin: 5px 0; }
          </style>
        </head>
        <body>${htmlContent}</body>
        </html>
      `)
      printWindow.document.close()
      printWindow.focus()
      setTimeout(() => {
        printWindow.print()
      }, 500)
    }
  }
}

// Export singleton instance
export const pdfExporter = PDFExporter.getInstance()

// Utility function for easy access
export async function exportPresentationToPDF(
  htmlContent: string, 
  filename: string = 'presentation.pdf',
  options: PDFExportOptions = {}
): Promise<void> {
  // Ensure libraries are loaded
  if (!PDFExporter.isPDFExportAvailable()) {
    await PDFExporter.loadPDFLibraries()
  }

  const exporter = PDFExporter.getInstance()
  await exporter.downloadPDF(htmlContent, filename, options)
}
