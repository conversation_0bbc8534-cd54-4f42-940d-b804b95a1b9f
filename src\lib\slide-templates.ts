export interface SlideTemplate {
  id: string
  name: string
  description: string
  category: 'business' | 'academic' | 'creative' | 'technical' | 'minimal'
  language: 'ar' | 'en'
  direction: 'rtl' | 'ltr'
  fonts: string[]
  dependencies: string[]
  preview: string
  css: string
  structure: {
    titleSlide: string
    contentSlide: string
    chartSlide: string
    comparisonSlide: string
    conclusionSlide: string
  }
  chartTypes: ('doughnut' | 'radar' | 'bar' | 'line')[]
  colorScheme: {
    primary: string
    secondary: string
    accent: string
    gradient: string
  }
}

export const slideTemplates: SlideTemplate[] = [
  {
    id: 'arabic-professional',
    name: 'Arabic Professional',
    description: 'قالب احترافي عربي متقدم مع رسوم بيانية تفاعلية',
    category: 'business',
    language: 'ar',
    direction: 'rtl',
    fonts: ['Tajawal'],
    dependencies: ['chart.js', 'fontawesome'],
    preview: 'قالب احترافي عربي مع خلفية متدرجة وكروت شفافة',
    chartTypes: ['doughnut', 'radar', 'bar'],
    colorScheme: {
      primary: '#1a365d',
      secondary: '#2c5282',
      accent: '#ff9d45',
      gradient: 'linear-gradient(135deg, #1a365d, #2c5282)'
    },
    css: `
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: 'Tajawal', sans-serif;
        background-color: #f5f7fa;
        color: #333;
        direction: rtl;
      }

      .slide {
        width: 100vw;
        min-height: 100vh;
        max-width: 1280px;
        max-height: 720px;
        margin: 0 auto;
        position: relative;
        overflow: hidden;
        background: linear-gradient(135deg, #1a365d, #2c5282);
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
      }

      @media (max-width: 768px) {
        .slide {
          width: 100vw;
          min-height: 100vh;
          max-width: none;
          max-height: none;
          padding: 20px;
        }
      }

      .slide-header {
        padding: 40px 70px 20px;
        flex-shrink: 0;
      }

      .title {
        font-size: clamp(28px, 5vw, 52px);
        font-weight: 800;
        color: #ffffff;
        margin-bottom: 10px;
        text-shadow: 0 2px 4px rgba(0,0,0,0.2);
        line-height: 1.2;
        word-wrap: break-word;
        overflow-wrap: break-word;
      }

      .subtitle {
        font-size: clamp(16px, 3vw, 22px);
        color: #e2e8f0;
        opacity: 0.9;
        line-height: 1.4;
        word-wrap: break-word;
        overflow-wrap: break-word;
      }

      @media (max-width: 768px) {
        .slide-header {
          padding: 20px 20px 15px;
        }

        .title {
          font-size: clamp(24px, 6vw, 36px);
          margin-bottom: 8px;
        }

        .subtitle {
          font-size: clamp(14px, 4vw, 18px);
        }
      }

      .slide-content {
        display: flex;
        flex: 1;
        min-height: 0;
        padding: 0 70px 40px;
        gap: 30px;
        overflow: hidden;
      }

      .left-content, .right-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 20px;
        min-width: 0;
        overflow: hidden;
      }

      @media (max-width: 768px) {
        .slide-content {
          flex-direction: column;
          padding: 0 20px 20px;
          gap: 20px;
          overflow-y: auto;
        }

        .left-content, .right-content {
          flex: none;
          min-height: auto;
        }
      }

      .card {
        background: rgba(255,255,255,0.08);
        border-radius: 16px;
        padding: 20px;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        backdrop-filter: blur(5px);
        border: 1px solid rgba(255,255,255,0.1);
        word-wrap: break-word;
        overflow-wrap: break-word;
        box-sizing: border-box;
      }

      @media (max-width: 768px) {
        .card {
          padding: 15px;
          border-radius: 12px;
          margin-bottom: 15px;
        }
      }

      .card:hover {
        background: rgba(255,255,255,0.12);
        transform: translateY(-3px);
      }

      .card-header {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
        position: relative;
        z-index: 2;
      }

      .card-icon {
        background: linear-gradient(135deg, #ff9d45, #ff6b35);
        color: white;
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: 15px;
        box-shadow: 0 4px 15px rgba(255, 157, 69, 0.4);
        flex-shrink: 0;
      }

      .card-title {
        color: #ffffff;
        font-size: clamp(18px, 3vw, 24px);
        font-weight: 700;
        line-height: 1.3;
        word-wrap: break-word;
        overflow-wrap: break-word;
      }

      .highlight {
        color: #ff9d45;
        font-weight: 600;
      }

      .feature-list {
        display: flex;
        flex-direction: column;
        gap: 12px;
        position: relative;
        z-index: 2;
      }

      .feature-item {
        display: flex;
        align-items: flex-start;
        color: #e2e8f0;
        font-size: clamp(14px, 2.5vw, 18px);
        line-height: 1.5;
        margin-bottom: 8px;
        word-wrap: break-word;
        overflow-wrap: break-word;
      }

      @media (max-width: 768px) {
        .feature-item {
          margin-bottom: 6px;
          align-items: flex-start;
        }
      }

      .feature-icon {
        color: #ff9d45;
        font-size: 20px;
        margin-left: 10px;
        flex-shrink: 0;
      }

      .chart-container {
        height: 200px;
        position: relative;
        margin-top: 10px;
      }

      .budget-meter {
        height: 10px;
        background: rgba(255,255,255,0.1);
        border-radius: 5px;
        margin-top: 15px;
        overflow: hidden;
      }

      .budget-level {
        height: 100%;
        border-radius: 5px;
        background: linear-gradient(90deg, #ff9d45, #ff6b35);
      }

      .budget-label {
        display: flex;
        justify-content: space-between;
        margin-top: 5px;
        color: #e2e8f0;
        font-size: 14px;
      }

      .overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-image: url('https://picsum.photos/seed/business-planning-feasibility/1280/720.jpg');
        background-size: cover;
        background-position: center;
        opacity: 0.1;
        z-index: 0;
      }

      .content-wrapper {
        position: relative;
        z-index: 1;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
      }

      .bg-icon {
        position: absolute;
        bottom: -20px;
        left: -20px;
        font-size: 150px;
        color: rgba(255,255,255,0.03);
        z-index: 1;
      }

      .price-tag {
        position: absolute;
        top: 15px;
        left: 15px;
        background: linear-gradient(135deg, #4ade80, #22c55e);
        color: white;
        font-size: 14px;
        font-weight: 600;
        padding: 4px 12px;
        border-radius: 20px;
        z-index: 2;
      }

      /* Additional responsive typography */
      h1, h2, h3, h4, h5, h6 {
        word-wrap: break-word;
        overflow-wrap: break-word;
        line-height: 1.3;
      }

      p {
        word-wrap: break-word;
        overflow-wrap: break-word;
        line-height: 1.6;
        font-size: clamp(14px, 2.5vw, 16px);
      }

      ul, ol {
        word-wrap: break-word;
        overflow-wrap: break-word;
      }

      li {
        word-wrap: break-word;
        overflow-wrap: break-word;
        margin-bottom: 5px;
      }

      /* Prevent horizontal overflow */
      * {
        max-width: 100%;
        box-sizing: border-box;
      }

      /* Responsive images and media */
      img, video, iframe {
        max-width: 100%;
        height: auto;
      }

      /* Mobile-specific adjustments */
      @media (max-width: 480px) {
        .slide-header {
          padding: 15px 15px 10px;
        }

        .slide-content {
          padding: 0 15px 15px;
          gap: 15px;
        }

        .card {
          padding: 12px;
          border-radius: 10px;
        }

        .title {
          font-size: clamp(20px, 7vw, 32px);
        }

        .subtitle {
          font-size: clamp(12px, 5vw, 16px);
        }
      }
    `,
    structure: {
      titleSlide: `
        <div class="slide">
          <div class="overlay"></div>
          <div class="content-wrapper">
            <div class="slide-header">
              <h1 class="title">{{title}}</h1>
              <p class="subtitle">{{subtitle}}</p>
            </div>
          </div>
        </div>
      `,
      contentSlide: `
        <div class="slide">
          <div class="overlay"></div>
          <div class="content-wrapper">
            <div class="slide-header">
              <h1 class="title">{{title}}</h1>
            </div>
            <div class="slide-content">
              <div class="left-content">
                {{leftContent}}
              </div>
              <div class="right-content">
                {{rightContent}}
              </div>
            </div>
          </div>
        </div>
      `,
      chartSlide: `
        <div class="slide">
          <div class="overlay"></div>
          <div class="content-wrapper">
            <div class="slide-header">
              <h1 class="title">{{title}}</h1>
            </div>
            <div class="slide-content">
              <div class="card">
                <div class="card-header">
                  <div class="card-icon">
                    <i class="fas fa-chart-pie"></i>
                  </div>
                  <h2 class="card-title">{{chartTitle}}</h2>
                </div>
                <div class="chart-container">
                  <canvas id="{{chartId}}"></canvas>
                </div>
              </div>
            </div>
          </div>
        </div>
      `,
      comparisonSlide: `
        <div class="slide">
          <div class="overlay"></div>
          <div class="content-wrapper">
            <div class="slide-header">
              <h1 class="title">{{title}}</h1>
            </div>
            <div class="slide-content">
              {{comparisonContent}}
            </div>
          </div>
        </div>
      `,
      conclusionSlide: `
        <div class="slide">
          <div class="overlay"></div>
          <div class="content-wrapper">
            <div class="slide-header">
              <h1 class="title">{{title}}</h1>
            </div>
            <div class="slide-content">
              <div class="card">
                <div class="card-header">
                  <div class="card-icon">
                    <i class="fas fa-check-circle"></i>
                  </div>
                  <h2 class="card-title">الخلاصة</h2>
                </div>
                <div class="feature-list">
                  {{conclusionPoints}}
                </div>
              </div>
            </div>
          </div>
        </div>
      `
    }
  },
  {
    id: 'modern-business',
    name: 'Modern Business',
    description: 'Professional business presentation with clean design',
    category: 'business',
    language: 'en',
    direction: 'ltr',
    fonts: ['Inter'],
    dependencies: [],
    preview: '/templates/modern-business-preview.jpg',
    chartTypes: ['doughnut', 'bar'],
    colorScheme: {
      primary: '#2563eb',
      secondary: '#1e40af',
      accent: '#3b82f6',
      gradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
    },
    css: `
      :root {
        --primary-color: #2563eb;
        --secondary-color: #1e40af;
        --accent-color: #3b82f6;
        --text-color: #1f2937;
        --bg-color: #ffffff;
        --gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      }
      
      body {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        margin: 0;
        padding: 0;
        background: var(--bg-color);
        color: var(--text-color);
        line-height: 1.6;
      }
      
      .slide {
        width: 100vw;
        height: 100vh;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        padding: 60px;
        box-sizing: border-box;
        position: relative;
        overflow: hidden;
      }
      
      .slide::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--gradient);
        z-index: 10;
      }
      
      .slide h1 {
        font-size: 3.5rem;
        font-weight: 700;
        margin-bottom: 1rem;
        text-align: center;
        background: var(--gradient);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }
      
      .slide h2 {
        font-size: 2.5rem;
        font-weight: 600;
        margin-bottom: 2rem;
        color: var(--primary-color);
        text-align: center;
      }
      
      .slide h3 {
        font-size: 1.8rem;
        font-weight: 500;
        margin-bottom: 1rem;
        color: var(--secondary-color);
      }
      
      .slide p {
        font-size: 1.2rem;
        max-width: 800px;
        text-align: center;
        margin-bottom: 1.5rem;
        opacity: 0.9;
      }
      
      .slide ul {
        font-size: 1.1rem;
        max-width: 700px;
        text-align: left;
      }
      
      .slide li {
        margin-bottom: 0.8rem;
        padding-left: 1rem;
        position: relative;
      }
      
      .slide li::before {
        content: '▶';
        position: absolute;
        left: 0;
        color: var(--accent-color);
        font-weight: bold;
      }
      
      .highlight {
        background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
        padding: 0.2rem 0.5rem;
        border-radius: 4px;
        font-weight: 600;
      }
      
      .slide-number {
        position: absolute;
        bottom: 30px;
        right: 30px;
        font-size: 0.9rem;
        opacity: 0.6;
      }
    `,
    structure: {
      titleSlide: `
        <div class="slide">
          <h1>{{title}}</h1>
          <p style="font-size: 1.4rem; opacity: 0.8;">{{subtitle}}</p>
          <div style="margin-top: 3rem; font-size: 1rem; opacity: 0.6;">
            <p>{{author}} • {{date}}</p>
          </div>
          <div class="slide-number">1</div>
        </div>
      `,
      contentSlide: `
        <div class="slide">
          <h2>{{title}}</h2>
          <div style="max-width: 900px;">
            {{content}}
          </div>
          <div class="slide-number">{{slideNumber}}</div>
        </div>
      `,
      chartSlide: `
        <div class="slide">
          <h2>{{title}}</h2>
          <div style="max-width: 900px;">
            <div class="chart-container">
              <canvas id="chart-{{slideNumber}}"></canvas>
            </div>
          </div>
          <div class="slide-number">{{slideNumber}}</div>
        </div>
      `,
      comparisonSlide: `
        <div class="slide">
          <h2>{{title}}</h2>
          <div style="max-width: 900px;">
            {{content}}
          </div>
          <div class="slide-number">{{slideNumber}}</div>
        </div>
      `,
      conclusionSlide: `
        <div class="slide">
          <h1 style="font-size: 2.8rem;">{{title}}</h1>
          <div style="max-width: 800px; margin-top: 2rem;">
            {{content}}
          </div>
          <div style="margin-top: 3rem; padding: 2rem; background: linear-gradient(135deg, #667eea20, #764ba220); border-radius: 12px; border-left: 4px solid var(--primary-color);">
            <p style="font-size: 1.3rem; font-weight: 600; margin: 0;">Thank You!</p>
            <p style="margin: 0.5rem 0 0 0; opacity: 0.8;">Questions & Discussion</p>
          </div>
          <div class="slide-number">{{slideNumber}}</div>
        </div>
      `
    }
  },
  
  {
    id: 'academic-research',
    name: 'Academic Research',
    description: 'Clean academic style for research presentations',
    category: 'academic',
    language: 'en',
    direction: 'ltr',
    fonts: ['Georgia', 'Times New Roman'],
    dependencies: [],
    preview: '/templates/academic-preview.jpg',
    chartTypes: ['bar', 'line'],
    colorScheme: {
      primary: '#1f2937',
      secondary: '#374151',
      accent: '#059669',
      gradient: 'linear-gradient(135deg, #1f2937 0%, #374151 100%)'
    },
    css: `
      :root {
        --primary-color: #1f2937;
        --secondary-color: #374151;
        --accent-color: #059669;
        --text-color: #111827;
        --bg-color: #ffffff;
        --light-bg: #f9fafb;
      }
      
      body {
        font-family: 'Georgia', 'Times New Roman', serif;
        margin: 0;
        padding: 0;
        background: var(--bg-color);
        color: var(--text-color);
        line-height: 1.7;
      }
      
      .slide {
        width: 100vw;
        height: 100vh;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        padding: 80px;
        box-sizing: border-box;
        position: relative;
      }
      
      .slide h1 {
        font-size: 3rem;
        font-weight: 400;
        margin-bottom: 1.5rem;
        text-align: center;
        color: var(--primary-color);
        border-bottom: 3px solid var(--accent-color);
        padding-bottom: 1rem;
      }
      
      .slide h2 {
        font-size: 2.2rem;
        font-weight: 400;
        margin-bottom: 2rem;
        color: var(--primary-color);
        text-align: center;
      }
      
      .slide h3 {
        font-size: 1.6rem;
        font-weight: 500;
        margin-bottom: 1rem;
        color: var(--secondary-color);
      }
      
      .slide p {
        font-size: 1.1rem;
        max-width: 850px;
        text-align: justify;
        margin-bottom: 1.5rem;
        text-indent: 1.5rem;
      }
      
      .slide ul {
        font-size: 1rem;
        max-width: 750px;
        text-align: left;
      }
      
      .slide li {
        margin-bottom: 0.6rem;
        padding-left: 0.5rem;
      }
      
      .citation {
        font-style: italic;
        font-size: 0.9rem;
        opacity: 0.8;
        margin-top: 1rem;
      }
      
      .slide-number {
        position: absolute;
        bottom: 30px;
        right: 30px;
        font-size: 0.9rem;
        opacity: 0.6;
      }
      
      .header-line {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 2px;
        background: var(--accent-color);
      }
    `,
    structure: {
      titleSlide: `
        <div class="slide">
          <div class="header-line"></div>
          <h1>{{title}}</h1>
          <p style="font-size: 1.3rem; text-align: center; text-indent: 0;">{{subtitle}}</p>
          <div style="margin-top: 4rem; text-align: center; font-size: 1rem;">
            <p style="text-indent: 0;"><strong>{{author}}</strong></p>
            <p style="text-indent: 0; opacity: 0.7;">{{institution}}</p>
            <p style="text-indent: 0; opacity: 0.6;">{{date}}</p>
          </div>
          <div class="slide-number">1</div>
        </div>
      `,
      contentSlide: `
        <div class="slide">
          <div class="header-line"></div>
          <h2>{{title}}</h2>
          <div style="max-width: 900px;">
            {{content}}
          </div>
          <div class="slide-number">{{slideNumber}}</div>
        </div>
      `,
      chartSlide: `
        <div class="slide">
          <div class="header-line"></div>
          <h2>{{title}}</h2>
          <div style="max-width: 900px;">
            <div class="chart-container">
              <canvas id="{{chartId}}"></canvas>
            </div>
          </div>
          <div class="slide-number">{{slideNumber}}</div>
        </div>
      `,
      comparisonSlide: `
        <div class="slide">
          <div class="header-line"></div>
          <h2>{{title}}</h2>
          <div style="max-width: 900px;">
            {{comparisonContent}}
          </div>
          <div class="slide-number">{{slideNumber}}</div>
        </div>
      `,
      conclusionSlide: `
        <div class="slide">
          <div class="header-line"></div>
          <h1 style="font-size: 2.5rem;">{{title}}</h1>
          <div style="max-width: 800px; margin-top: 2rem;">
            {{content}}
          </div>
          <div style="margin-top: 3rem; padding: 2rem; background: var(--light-bg); border-radius: 8px; border-left: 4px solid var(--accent-color);">
            <p style="font-size: 1.2rem; font-weight: 500; margin: 0; text-indent: 0;">References & Further Reading</p>
            <p style="margin: 0.5rem 0 0 0; opacity: 0.8; text-indent: 0;">Available upon request</p>
          </div>
          <div class="slide-number">{{slideNumber}}</div>
        </div>
      `
    }
  }
]

export function getTemplate(templateId: string): SlideTemplate | undefined {
  return slideTemplates.find(template => template.id === templateId)
}

export function getTemplatesByCategory(category: SlideTemplate['category']): SlideTemplate[] {
  return slideTemplates.filter(template => template.category === category)
}

export function getAllTemplates(): SlideTemplate[] {
  return slideTemplates
}
