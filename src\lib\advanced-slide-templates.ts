// Advanced Slide Templates with Modern Designs

import { ImageSearchResult } from './image-search'
import { ChartData } from './chart-generator'

export interface AdvancedSlideTemplate {
  id: string
  name: string
  description: string
  category: 'hero' | 'content' | 'chart' | 'comparison' | 'timeline' | 'quote' | 'summary'
  supportsImages: boolean
  supportsCharts: boolean
  layout: 'full-width' | 'split' | 'centered' | 'grid'
}

export interface SlideContent {
  type: 'hero' | 'content' | 'chart' | 'comparison' | 'timeline' | 'quote' | 'summary'
  title: string
  subtitle?: string
  content?: string
  bulletPoints?: string[]
  image?: ImageSearchResult
  chart?: ChartData
  statistics?: { [key: string]: string | number }
  quote?: { text: string; author: string }
  timeline?: { year: string; event: string }[]
  comparison?: { left: any; right: any }
}

class AdvancedSlideTemplateService {
  private readonly templates: AdvancedSlideTemplate[] = [
    {
      id: 'hero-slide',
      name: 'Hero Slide',
      description: 'Full-width hero slide with background image',
      category: 'hero',
      supportsImages: true,
      supportsCharts: false,
      layout: 'full-width'
    },
    {
      id: 'content-image',
      name: 'Content with Image',
      description: 'Split layout with content and image',
      category: 'content',
      supportsImages: true,
      supportsCharts: false,
      layout: 'split'
    },
    {
      id: 'chart-slide',
      name: 'Chart Slide',
      description: 'Dedicated slide for charts and data visualization',
      category: 'chart',
      supportsImages: false,
      supportsCharts: true,
      layout: 'centered'
    },
    {
      id: 'comparison-slide',
      name: 'Comparison Slide',
      description: 'Side-by-side comparison layout',
      category: 'comparison',
      supportsImages: true,
      supportsCharts: false,
      layout: 'split'
    },
    {
      id: 'timeline-slide',
      name: 'Timeline Slide',
      description: 'Timeline layout for historical events',
      category: 'timeline',
      supportsImages: true,
      supportsCharts: false,
      layout: 'centered'
    },
    {
      id: 'quote-slide',
      name: 'Quote Slide',
      description: 'Centered quote with attribution',
      category: 'quote',
      supportsImages: true,
      supportsCharts: false,
      layout: 'centered'
    },
    {
      id: 'summary-slide',
      name: 'Summary Slide',
      description: 'Summary with key statistics',
      category: 'summary',
      supportsImages: true,
      supportsCharts: true,
      layout: 'grid'
    }
  ]

  /**
   * Generate Ultra Advanced Hero Slide HTML
   */
  generateHeroSlide(content: SlideContent): string {
    const backgroundImage = content.image?.url || 'https://images.unsplash.com/photo-1557804506-669a67965ba0?w=1200'

    return `
      <div class="slide hero-slide slide-element-interactive" data-parallax="0.5">
        <div class="hero-background" style="background-image: url('${backgroundImage}')" data-parallax="0.3"></div>
        <div class="hero-overlay"></div>

        <!-- Floating Geometric Shapes -->
        <div style="
          position: absolute;
          top: 20%;
          left: 10%;
          width: 100px;
          height: 100px;
          background: linear-gradient(45deg, rgba(102, 126, 234, 0.2), rgba(240, 147, 251, 0.2));
          border-radius: 50%;
          animation: floatShape1 8s ease-in-out infinite;
          z-index: 1;
        "></div>

        <div style="
          position: absolute;
          top: 60%;
          right: 15%;
          width: 80px;
          height: 80px;
          background: linear-gradient(45deg, rgba(245, 87, 108, 0.2), rgba(255, 157, 69, 0.2));
          transform: rotate(45deg);
          animation: floatShape2 10s ease-in-out infinite reverse;
          z-index: 1;
        "></div>

        <div class="hero-content">
          <h1 class="hero-title slide-element-interactive">${content.title}</h1>
          ${content.subtitle ? `<p class="hero-subtitle slide-element-interactive">${content.subtitle}</p>` : ''}
          <div class="hero-decoration slide-element-interactive"></div>

          <!-- Advanced Call-to-Action -->
          <div style="
            margin-top: 3rem;
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
          ">
            <button class="slide-element-interactive" style="
              background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
              border: 1px solid rgba(255, 255, 255, 0.3);
              color: white;
              padding: 12px 24px;
              border-radius: 25px;
              font-family: 'Tajawal', sans-serif;
              font-weight: 500;
              cursor: pointer;
              transition: all 0.3s ease;
              backdrop-filter: blur(10px);
            " onmouseover="this.style.background='linear-gradient(135deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.2))'" onmouseout="this.style.background='linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1))'">
              🚀 ابدأ الاستكشاف
            </button>
          </div>
        </div>

        <div class="hero-branding" style="
          background: rgba(255, 255, 255, 0.1);
          backdrop-filter: blur(10px);
          padding: 8px 16px;
          border-radius: 20px;
          border: 1px solid rgba(255, 255, 255, 0.2);
        ">
          <span>✨ تم إنشاؤه بواسطة WIDDX AI المتقدم</span>
        </div>

        <style>
          @keyframes floatShape1 {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
          }

          @keyframes floatShape2 {
            0%, 100% { transform: rotate(45deg) translateY(0px); }
            50% { transform: rotate(225deg) translateY(-15px); }
          }
        </style>
      </div>
    `
  }

  /**
   * Generate Ultra Advanced Content with Image Slide HTML
   */
  generateContentImageSlide(content: SlideContent): string {
    return `
      <div class="slide content-image-slide slide-element-interactive">
        <!-- Advanced Particle Background -->
        <div style="
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background-image:
            radial-gradient(circle at 25% 25%, rgba(102, 126, 234, 0.1) 0%, transparent 50%),
            radial-gradient(circle at 75% 75%, rgba(240, 147, 251, 0.1) 0%, transparent 50%);
          animation: particleMove 20s ease-in-out infinite;
          pointer-events: none;
        "></div>

        <div class="slide-header slide-element-interactive">
          <h1 class="slide-title">${content.title}</h1>
          ${content.subtitle ? `<p class="slide-subtitle">${content.subtitle}</p>` : ''}

          <!-- Progress Indicator -->
          <div style="
            width: 100px;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
            margin: 20px auto 0;
            position: relative;
            overflow: hidden;
          ">
            <div style="
              position: absolute;
              top: 0;
              left: -100%;
              width: 100%;
              height: 100%;
              background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.6), transparent);
              animation: progressShine 3s ease-in-out infinite;
            "></div>
          </div>
        </div>

        <div class="slide-body">
          <div class="content-section slide-element-interactive">
            ${content.content ? `<div class="content-text">${content.content}</div>` : ''}
            ${content.bulletPoints ? `
              <ul class="bullet-list">
                ${content.bulletPoints.map((point, index) => `
                  <li class="bullet-item slide-element-interactive" style="
                    animation-delay: ${index * 0.1}s;
                    transform: translateX(-30px);
                    opacity: 0;
                    animation: slideInRight 0.6s ease forwards;
                  ">${point}</li>
                `).join('')}
              </ul>
            ` : ''}

            <!-- Interactive Stats -->
            <div style="
              display: grid;
              grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
              gap: 1rem;
              margin-top: 2rem;
            ">
              <div class="slide-element-interactive" style="
                background: rgba(255, 255, 255, 0.1);
                backdrop-filter: blur(15px);
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 15px;
                padding: 1rem;
                text-align: center;
                transition: all 0.3s ease;
              " onmouseover="this.style.transform='scale(1.05)'" onmouseout="this.style.transform='scale(1)'">
                <div style="font-size: 1.5rem; font-weight: bold; color: #667eea;">95%</div>
                <div style="font-size: 0.9rem; opacity: 0.8;">دقة المعلومات</div>
              </div>

              <div class="slide-element-interactive" style="
                background: rgba(255, 255, 255, 0.1);
                backdrop-filter: blur(15px);
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 15px;
                padding: 1rem;
                text-align: center;
                transition: all 0.3s ease;
              " onmouseover="this.style.transform='scale(1.05)'" onmouseout="this.style.transform='scale(1)'">
                <div style="font-size: 1.5rem; font-weight: bold; color: #f093fb;">100%</div>
                <div style="font-size: 0.9rem; opacity: 0.8;">ذكاء اصطناعي</div>
              </div>
            </div>
          </div>

          ${content.image ? `
            <div class="image-section slide-element-interactive">
              <div style="position: relative;">
                <img src="${content.image.url}" alt="${content.image.alt}" class="slide-image" />

                <!-- Image Overlay Effects -->
                <div style="
                  position: absolute;
                  top: 10px;
                  right: 10px;
                  background: rgba(0, 0, 0, 0.7);
                  color: white;
                  padding: 5px 10px;
                  border-radius: 15px;
                  font-size: 0.8rem;
                  backdrop-filter: blur(10px);
                ">
                  🎨 AI Generated
                </div>
              </div>
              <p class="image-caption">${content.image.description}</p>
            </div>
          ` : ''}
        </div>

        <style>
          @keyframes particleMove {
            0%, 100% { transform: translate(0, 0); }
            25% { transform: translate(10px, -10px); }
            50% { transform: translate(-5px, 5px); }
            75% { transform: translate(5px, -5px); }
          }

          @keyframes progressShine {
            0% { left: -100%; }
            100% { left: 100%; }
          }

          @keyframes slideInRight {
            from { transform: translateX(-30px); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
          }
        </style>
      </div>
    `
  }

  /**
   * Generate Chart Slide HTML
   */
  generateChartSlide(content: SlideContent): string {
    const chartId = `chart-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    
    return `
      <div class="slide chart-slide">
        <div class="slide-header">
          <h1 class="slide-title">${content.title}</h1>
          ${content.subtitle ? `<p class="slide-subtitle">${content.subtitle}</p>` : ''}
        </div>
        <div class="chart-container">
          <canvas id="${chartId}" class="chart-canvas"></canvas>
          ${content.chart ? `
            <script>
              document.addEventListener('DOMContentLoaded', function() {
                const ctx = document.getElementById('${chartId}');
                if (ctx && typeof Chart !== 'undefined') {
                  new Chart(ctx, ${JSON.stringify({
                    type: content.chart.type,
                    data: content.chart.data,
                    options: {
                      ...content.chart.options,
                      responsive: true,
                      maintainAspectRatio: false
                    }
                  })});
                }
              });
            </script>
          ` : ''}
        </div>
        ${content.chart?.description ? `
          <div class="chart-description">
            <p>${content.chart.description}</p>
          </div>
        ` : ''}
      </div>
    `
  }

  /**
   * Generate Timeline Slide HTML
   */
  generateTimelineSlide(content: SlideContent): string {
    return `
      <div class="slide timeline-slide">
        <div class="slide-header">
          <h1 class="slide-title">${content.title}</h1>
          ${content.subtitle ? `<p class="slide-subtitle">${content.subtitle}</p>` : ''}
        </div>
        <div class="timeline-container">
          ${content.timeline ? content.timeline.map((item, index) => `
            <div class="timeline-item ${index % 2 === 0 ? 'timeline-left' : 'timeline-right'}">
              <div class="timeline-marker"></div>
              <div class="timeline-content">
                <div class="timeline-year">${item.year}</div>
                <div class="timeline-event">${item.event}</div>
              </div>
            </div>
          `).join('') : ''}
        </div>
      </div>
    `
  }

  /**
   * Generate Quote Slide HTML
   */
  generateQuoteSlide(content: SlideContent): string {
    return `
      <div class="slide quote-slide">
        ${content.image ? `<div class="quote-background" style="background-image: url('${content.image.url}')"></div>` : ''}
        <div class="quote-overlay"></div>
        <div class="quote-container">
          <div class="quote-mark">"</div>
          <blockquote class="quote-text">
            ${content.quote?.text || content.content}
          </blockquote>
          <cite class="quote-author">
            — ${content.quote?.author || 'مجهول'}
          </cite>
        </div>
      </div>
    `
  }

  /**
   * Generate Summary Slide HTML
   */
  generateSummarySlide(content: SlideContent): string {
    return `
      <div class="slide summary-slide">
        <div class="slide-header">
          <h1 class="slide-title">${content.title}</h1>
          ${content.subtitle ? `<p class="slide-subtitle">${content.subtitle}</p>` : ''}
        </div>
        <div class="summary-grid">
          ${content.bulletPoints ? `
            <div class="summary-points">
              <h3>النقاط الرئيسية</h3>
              <ul class="summary-list">
                ${content.bulletPoints.map(point => `<li class="summary-item">${point}</li>`).join('')}
              </ul>
            </div>
          ` : ''}
          
          ${content.statistics ? `
            <div class="summary-stats">
              <h3>إحصائيات مهمة</h3>
              <div class="stats-grid">
                ${Object.entries(content.statistics).map(([key, value]) => `
                  <div class="stat-item">
                    <div class="stat-value">${value}</div>
                    <div class="stat-label">${key}</div>
                  </div>
                `).join('')}
              </div>
            </div>
          ` : ''}
        </div>
      </div>
    `
  }

  /**
   * Generate slide based on content type
   */
  generateSlide(content: SlideContent): string {
    switch (content.type) {
      case 'hero':
        return this.generateHeroSlide(content)
      case 'content':
        return this.generateContentImageSlide(content)
      case 'chart':
        return this.generateChartSlide(content)
      case 'timeline':
        return this.generateTimelineSlide(content)
      case 'quote':
        return this.generateQuoteSlide(content)
      case 'summary':
        return this.generateSummarySlide(content)
      default:
        return this.generateContentImageSlide(content)
    }
  }

  /**
   * Get advanced CSS for all slide types
   */
  getAdvancedCSS(): string {
    return `
      /* Ultra Advanced Slide Styles with 3D Effects */
      @import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;600;700;800;900&display=swap');

      :root {
        --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        --glass-bg: rgba(255, 255, 255, 0.1);
        --glass-border: rgba(255, 255, 255, 0.2);
        --shadow-light: 0 8px 32px rgba(31, 38, 135, 0.37);
        --shadow-heavy: 0 20px 60px rgba(0, 0, 0, 0.3);
        --text-glow: 0 0 20px rgba(255, 255, 255, 0.5);
      }

      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      .slide {
        width: 100vw;
        min-height: 100vh;
        position: relative;
        display: flex;
        flex-direction: column;
        font-family: 'Tajawal', sans-serif;
        direction: rtl;
        overflow: hidden;
        perspective: 1000px;
        transform-style: preserve-3d;
        background: linear-gradient(45deg, #1e3c72, #2a5298, #667eea, #764ba2);
        background-size: 400% 400%;
        animation: gradientShift 15s ease infinite;
      }

      @keyframes gradientShift {
        0% { background-position: 0% 50%; }
        50% { background-position: 100% 50%; }
        100% { background-position: 0% 50%; }
      }

      /* Floating Particles Background */
      .slide::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-image:
          radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
          radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
          radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
        animation: floatingParticles 20s ease-in-out infinite;
        pointer-events: none;
      }

      @keyframes floatingParticles {
        0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0.7; }
        50% { transform: translateY(-20px) rotate(180deg); opacity: 1; }
      }

      /* Ultra Advanced Hero Slide Styles */
      .hero-slide {
        justify-content: center;
        align-items: center;
        text-align: center;
        color: white;
        position: relative;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        background-size: 300% 300%;
        animation: heroGradientFlow 12s ease infinite;
      }

      @keyframes heroGradientFlow {
        0% { background-position: 0% 50%; }
        50% { background-position: 100% 50%; }
        100% { background-position: 0% 50%; }
      }

      .hero-background {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
        filter: brightness(0.4) contrast(1.2) saturate(1.3);
        transform: scale(1.1);
        animation: heroBackgroundZoom 20s ease-in-out infinite;
      }

      @keyframes heroBackgroundZoom {
        0%, 100% { transform: scale(1.1) rotate(0deg); }
        50% { transform: scale(1.2) rotate(1deg); }
      }

      .hero-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background:
          radial-gradient(circle at 30% 70%, rgba(102, 126, 234, 0.4) 0%, transparent 50%),
          radial-gradient(circle at 70% 30%, rgba(240, 147, 251, 0.4) 0%, transparent 50%),
          linear-gradient(135deg, rgba(26, 54, 93, 0.6), rgba(44, 82, 130, 0.4));
        backdrop-filter: blur(2px);
        animation: overlayPulse 8s ease-in-out infinite;
      }

      @keyframes overlayPulse {
        0%, 100% { opacity: 0.8; }
        50% { opacity: 1; }
      }

      .hero-content {
        position: relative;
        z-index: 2;
        max-width: 900px;
        padding: 0 2rem;
        transform: translateZ(50px);
        animation: heroContentFloat 6s ease-in-out infinite;
      }

      @keyframes heroContentFloat {
        0%, 100% { transform: translateZ(50px) translateY(0px); }
        50% { transform: translateZ(50px) translateY(-10px); }
      }

      .hero-title {
        font-size: clamp(2.5rem, 6vw, 5rem);
        font-weight: 900;
        margin-bottom: 1.5rem;
        background: linear-gradient(135deg, #ffffff, #f0f9ff, #e0f2fe);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        text-shadow: 0 0 30px rgba(255, 255, 255, 0.5);
        line-height: 1.1;
        position: relative;
        animation: titleGlow 4s ease-in-out infinite;
      }

      @keyframes titleGlow {
        0%, 100% { filter: drop-shadow(0 0 20px rgba(255, 255, 255, 0.3)); }
        50% { filter: drop-shadow(0 0 40px rgba(255, 255, 255, 0.6)); }
      }

      .hero-title::before {
        content: '';
        position: absolute;
        top: -10px;
        left: -10px;
        right: -10px;
        bottom: -10px;
        background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
        border-radius: 20px;
        z-index: -1;
        animation: titleShimmer 3s linear infinite;
      }

      @keyframes titleShimmer {
        0% { transform: translateX(-100%); }
        100% { transform: translateX(100%); }
      }

      .hero-subtitle {
        font-size: clamp(1.2rem, 3vw, 2rem);
        opacity: 0.95;
        margin-bottom: 2.5rem;
        font-weight: 400;
        text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        animation: subtitleFade 5s ease-in-out infinite;
      }

      @keyframes subtitleFade {
        0%, 100% { opacity: 0.95; }
        50% { opacity: 1; }
      }

      .hero-decoration {
        width: 150px;
        height: 6px;
        background: linear-gradient(90deg, #ff9d45, #ff6b35, #f093fb, #667eea);
        background-size: 300% 100%;
        margin: 0 auto;
        border-radius: 10px;
        position: relative;
        animation: decorationFlow 4s ease infinite;
        box-shadow: 0 0 20px rgba(255, 157, 69, 0.5);
      }

      @keyframes decorationFlow {
        0% { background-position: 0% 50%; }
        100% { background-position: 300% 50%; }
      }

      .hero-decoration::after {
        content: '';
        position: absolute;
        top: -3px;
        left: -3px;
        right: -3px;
        bottom: -3px;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
        border-radius: 10px;
        animation: decorationGlow 2s ease-in-out infinite;
      }

      @keyframes decorationGlow {
        0%, 100% { opacity: 0; }
        50% { opacity: 1; }
      }

      .hero-branding {
        position: absolute;
        bottom: 2rem;
        right: 2rem;
        font-size: 0.9rem;
        opacity: 0.7;
        z-index: 2;
      }

      /* Ultra Advanced Content Image Slide Styles */
      .content-image-slide {
        background:
          linear-gradient(135deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
        background-size: 400% 400%;
        animation: contentBackgroundFlow 20s ease infinite;
        padding: 4rem 3rem;
        position: relative;
        overflow: hidden;
      }

      @keyframes contentBackgroundFlow {
        0% { background-position: 0% 50%; }
        25% { background-position: 100% 50%; }
        50% { background-position: 100% 100%; }
        75% { background-position: 0% 100%; }
        100% { background-position: 0% 50%; }
      }

      .content-image-slide::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background:
          radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
          radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
        pointer-events: none;
        animation: contentOverlayMove 15s ease-in-out infinite;
      }

      @keyframes contentOverlayMove {
        0%, 100% { transform: translate(0, 0); }
        50% { transform: translate(20px, -20px); }
      }

      .slide-header {
        text-align: center;
        margin-bottom: 4rem;
        position: relative;
        z-index: 2;
        background: var(--glass-bg);
        backdrop-filter: blur(20px);
        border: 1px solid var(--glass-border);
        border-radius: 25px;
        padding: 2rem;
        box-shadow: var(--shadow-light);
        transform: translateZ(30px);
        animation: headerFloat 8s ease-in-out infinite;
      }

      @keyframes headerFloat {
        0%, 100% { transform: translateZ(30px) translateY(0px); }
        50% { transform: translateZ(30px) translateY(-5px); }
      }

      .slide-title {
        font-size: clamp(2.2rem, 4vw, 3.5rem);
        font-weight: 900;
        background: linear-gradient(135deg, #ffffff, #f0f9ff, #e0f2fe);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        margin-bottom: 1.5rem;
        position: relative;
        text-shadow: var(--text-glow);
        animation: titlePulse 6s ease-in-out infinite;
      }

      @keyframes titlePulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.02); }
      }

      .slide-subtitle {
        font-size: clamp(1.1rem, 2vw, 1.6rem);
        color: rgba(255, 255, 255, 0.9);
        font-weight: 400;
        text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        animation: subtitleGlow 4s ease-in-out infinite;
      }

      @keyframes subtitleGlow {
        0%, 100% { text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3); }
        50% { text-shadow: 0 2px 20px rgba(255, 255, 255, 0.2); }
      }

      .slide-body {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 4rem;
        align-items: center;
        max-width: 1400px;
        margin: 0 auto;
        position: relative;
        z-index: 2;
        perspective: 1000px;
      }

      .content-section {
        background: var(--glass-bg);
        backdrop-filter: blur(25px);
        border: 1px solid var(--glass-border);
        border-radius: 30px;
        padding: 3rem;
        box-shadow: var(--shadow-light);
        transform: translateZ(20px);
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        animation: contentSectionFloat 10s ease-in-out infinite;
      }

      @keyframes contentSectionFloat {
        0%, 100% { transform: translateZ(20px) rotateY(0deg); }
        50% { transform: translateZ(25px) rotateY(2deg); }
      }

      .content-section:hover {
        transform: translateZ(30px) scale(1.02);
        box-shadow: 0 25px 50px rgba(31, 38, 135, 0.5);
        border-color: rgba(255, 255, 255, 0.4);
      }

      .content-text {
        font-size: 1.3rem;
        line-height: 1.9;
        color: rgba(255, 255, 255, 0.95);
        margin-bottom: 2.5rem;
        text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
        animation: textGlow 8s ease-in-out infinite;
      }

      @keyframes textGlow {
        0%, 100% { text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3); }
        50% { text-shadow: 0 1px 10px rgba(255, 255, 255, 0.1); }
      }

      .bullet-list {
        list-style: none;
        padding: 0;
        position: relative;
      }

      .bullet-item {
        display: flex;
        align-items: flex-start;
        margin-bottom: 1.5rem;
        font-size: 1.2rem;
        line-height: 1.7;
        color: rgba(255, 255, 255, 0.9);
        padding: 1rem;
        background: rgba(255, 255, 255, 0.05);
        border-radius: 15px;
        border: 1px solid rgba(255, 255, 255, 0.1);
        transition: all 0.3s ease;
        transform: translateX(0);
        animation: bulletItemSlide 0.6s ease forwards;
      }

      @keyframes bulletItemSlide {
        from { transform: translateX(-30px); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
      }

      .bullet-item:hover {
        background: rgba(255, 255, 255, 0.1);
        border-color: rgba(255, 255, 255, 0.3);
        transform: translateX(10px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
      }

      .bullet-item::before {
        content: '✨';
        font-size: 1.2rem;
        margin-left: 1rem;
        flex-shrink: 0;
        animation: bulletIconSpin 4s linear infinite;
      }

      @keyframes bulletIconSpin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }

      .bullet-item:nth-child(1)::before { content: '🚀'; }
      .bullet-item:nth-child(2)::before { content: '💎'; }
      .bullet-item:nth-child(3)::before { content: '⭐'; }
      .bullet-item:nth-child(4)::before { content: '🔥'; }
      .bullet-item:nth-child(5)::before { content: '💫'; }

      .image-section {
        text-align: center;
        position: relative;
        perspective: 1000px;
        animation: imageSectionFloat 12s ease-in-out infinite;
      }

      @keyframes imageSectionFloat {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-10px); }
      }

      .slide-image {
        width: 100%;
        max-width: 550px;
        height: auto;
        border-radius: 25px;
        box-shadow:
          0 25px 50px rgba(0, 0, 0, 0.3),
          0 0 0 1px rgba(255, 255, 255, 0.1),
          inset 0 1px 0 rgba(255, 255, 255, 0.2);
        transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
        transform: rotateY(0deg) rotateX(0deg) translateZ(0px);
        position: relative;
        overflow: hidden;
      }

      .slide-image::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
        transition: left 0.6s ease;
        z-index: 1;
      }

      .slide-image:hover::before {
        left: 100%;
      }

      .slide-image:hover {
        transform: rotateY(5deg) rotateX(5deg) translateZ(30px) scale(1.05);
        box-shadow:
          0 35px 70px rgba(0, 0, 0, 0.4),
          0 0 0 1px rgba(255, 255, 255, 0.2),
          inset 0 1px 0 rgba(255, 255, 255, 0.3),
          0 0 50px rgba(102, 126, 234, 0.3);
      }

      .image-section::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 120%;
        height: 120%;
        background: radial-gradient(circle, rgba(102, 126, 234, 0.1) 0%, transparent 70%);
        transform: translate(-50%, -50%);
        border-radius: 50%;
        animation: imageGlow 6s ease-in-out infinite;
        pointer-events: none;
        z-index: -1;
      }

      @keyframes imageGlow {
        0%, 100% { opacity: 0.3; transform: translate(-50%, -50%) scale(1); }
        50% { opacity: 0.6; transform: translate(-50%, -50%) scale(1.1); }
      }

      .image-caption {
        margin-top: 1.5rem;
        font-size: 1rem;
        color: rgba(255, 255, 255, 0.8);
        font-style: italic;
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        padding: 0.8rem 1.5rem;
        border-radius: 20px;
        border: 1px solid rgba(255, 255, 255, 0.2);
        display: inline-block;
        text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
        animation: captionFade 5s ease-in-out infinite;
      }

      @keyframes captionFade {
        0%, 100% { opacity: 0.8; }
        50% { opacity: 1; }
      }

      /* Ultra Advanced Chart Slide Styles */
      .chart-slide {
        background:
          linear-gradient(135deg, #0f0c29 0%, #302b63 35%, #24243e 100%),
          radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
          radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%);
        background-blend-mode: overlay;
        color: white;
        padding: 4rem 3rem;
        justify-content: center;
        position: relative;
        overflow: hidden;
      }

      .chart-slide::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background:
          linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.02) 50%, transparent 70%);
        animation: chartSlideShimmer 8s linear infinite;
        pointer-events: none;
      }

      @keyframes chartSlideShimmer {
        0% { transform: translateX(-100%); }
        100% { transform: translateX(100%); }
      }

      .chart-container {
        max-width: 900px;
        height: 500px;
        margin: 3rem auto;
        background:
          linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05)),
          radial-gradient(circle at 50% 50%, rgba(102, 126, 234, 0.1) 0%, transparent 50%);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 30px;
        padding: 3rem;
        backdrop-filter: blur(25px);
        box-shadow:
          0 25px 50px rgba(0, 0, 0, 0.3),
          inset 0 1px 0 rgba(255, 255, 255, 0.2);
        position: relative;
        transform: translateZ(20px);
        animation: chartContainerFloat 10s ease-in-out infinite;
      }

      @keyframes chartContainerFloat {
        0%, 100% { transform: translateZ(20px) rotateX(0deg); }
        50% { transform: translateZ(25px) rotateX(2deg); }
      }

      .chart-container::before {
        content: '';
        position: absolute;
        top: -2px;
        left: -2px;
        right: -2px;
        bottom: -2px;
        background: linear-gradient(45deg, #667eea, #764ba2, #f093fb, #f5576c);
        border-radius: 30px;
        z-index: -1;
        animation: chartBorderGlow 4s ease-in-out infinite;
      }

      @keyframes chartBorderGlow {
        0%, 100% { opacity: 0.3; }
        50% { opacity: 0.8; }
      }

      .chart-canvas {
        width: 100% !important;
        height: 100% !important;
        border-radius: 20px;
        filter: drop-shadow(0 10px 20px rgba(0, 0, 0, 0.2));
      }

      .chart-description {
        text-align: center;
        margin-top: 2.5rem;
        font-size: 1.3rem;
        opacity: 0.95;
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(15px);
        padding: 1.5rem 2rem;
        border-radius: 20px;
        border: 1px solid rgba(255, 255, 255, 0.2);
        max-width: 800px;
        margin-left: auto;
        margin-right: auto;
        text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        animation: descriptionPulse 6s ease-in-out infinite;
      }

      @keyframes descriptionPulse {
        0%, 100% { transform: scale(1); opacity: 0.95; }
        50% { transform: scale(1.01); opacity: 1; }
      }

      /* Advanced Interactive Elements */
      .slide-element-interactive {
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        cursor: pointer;
      }

      .slide-element-interactive:hover {
        transform: translateY(-5px) scale(1.02);
        filter: brightness(1.1);
      }

      /* Advanced Loading Animation */
      .slide-loading {
        position: relative;
        overflow: hidden;
      }

      .slide-loading::after {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        animation: slideLoading 2s infinite;
      }

      @keyframes slideLoading {
        0% { left: -100%; }
        100% { left: 100%; }
      }

      /* Advanced Scroll Indicators */
      .slide-scroll-indicator {
        position: fixed;
        top: 50%;
        right: 2rem;
        transform: translateY(-50%);
        z-index: 1000;
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
      }

      .scroll-dot {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.3);
        border: 2px solid rgba(255, 255, 255, 0.5);
        transition: all 0.3s ease;
        cursor: pointer;
      }

      .scroll-dot.active {
        background: rgba(255, 255, 255, 0.9);
        transform: scale(1.3);
        box-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
      }

      /* Ultra Advanced Responsive Design */
      @media (max-width: 1200px) {
        .slide-body {
          max-width: 1000px;
          gap: 3rem;
        }

        .chart-container {
          max-width: 750px;
          height: 450px;
        }
      }

      @media (max-width: 768px) {
        .slide-body {
          grid-template-columns: 1fr;
          gap: 2.5rem;
          padding: 0 1rem;
        }

        .slide {
          padding: 2rem 1rem;
        }

        .hero-content {
          padding: 0 1rem;
        }

        .content-section {
          padding: 2rem;
          border-radius: 20px;
        }

        .slide-header {
          padding: 1.5rem;
          border-radius: 20px;
          margin-bottom: 2rem;
        }

        .chart-container {
          height: 350px;
          padding: 2rem;
          border-radius: 20px;
        }

        .slide-image {
          max-width: 100%;
          border-radius: 20px;
        }

        .hero-title {
          font-size: clamp(2rem, 8vw, 3rem);
        }

        .slide-title {
          font-size: clamp(1.8rem, 6vw, 2.5rem);
        }
      }

      @media (max-width: 480px) {
        .slide {
          padding: 1.5rem 0.5rem;
        }

        .content-section {
          padding: 1.5rem;
        }

        .slide-header {
          padding: 1rem;
          margin-bottom: 1.5rem;
        }

        .chart-container {
          height: 300px;
          padding: 1.5rem;
        }

        .bullet-item {
          font-size: 1rem;
          padding: 0.8rem;
        }

        .content-text {
          font-size: 1.1rem;
        }
      }

      /* Advanced Print Styles */
      @media print {
        .slide {
          page-break-after: always;
          background: white !important;
          color: black !important;
          box-shadow: none !important;
        }

        .slide::before,
        .slide::after {
          display: none !important;
        }

        .hero-background {
          display: none !important;
        }

        .chart-container {
          background: white !important;
          border: 2px solid #ccc !important;
        }
      }

      /* Advanced Dark Mode Support */
      @media (prefers-color-scheme: dark) {
        .slide {
          filter: brightness(0.9);
        }
      }

      /* Advanced Reduced Motion Support */
      @media (prefers-reduced-motion: reduce) {
        * {
          animation-duration: 0.01ms !important;
          animation-iteration-count: 1 !important;
          transition-duration: 0.01ms !important;
        }
      }
    `
  }

  /**
   * Get templates list
   */
  getTemplates(): AdvancedSlideTemplate[] {
    return this.templates
  }
}

// Export singleton instance
export const advancedSlideTemplateService = new AdvancedSlideTemplateService()

// Export types
export type { SlideContent as AdvancedSlideContent }
