// Advanced HTML Generator for Professional Presentations

export class AdvancedHTMLGenerator {
  
  /**
   * Generate individual slide HTML
   */
  static generateSlideHTML(slide: any, index: number, isArabic: boolean): string {
    const slideClass = `slide slide-${index + 1} ${slide.type}-slide ${slide.layout}-layout`
    
    switch (slide.type) {
      case 'title':
        return this.generateTitleSlide(slide, slideClass, isArabic)
      case 'content':
        return this.generateContentSlide(slide, slideClass, isArabic)
      case 'conclusion':
        return this.generateConclusionSlide(slide, slideClass, isArabic)
      default:
        return this.generateContentSlide(slide, slideClass, isArabic)
    }
  }

  /**
   * Generate title slide HTML
   */
  private static generateTitleSlide(slide: any, slideClass: string, isArabic: boolean): string {
    return `
    <div class="${slideClass}" data-slide="${slide.type}">
        <div class="slide-background">
            <div class="gradient-overlay title-gradient"></div>
            <div class="background-image"></div>
            <div class="geometric-shapes">
                <div class="shape shape-1"></div>
                <div class="shape shape-2"></div>
                <div class="shape shape-3"></div>
            </div>
        </div>
        <div class="slide-content">
            <div class="title-container animate__animated animate__fadeInUp">
                <div class="title-icon">
                    <i class="fas fa-rocket"></i>
                </div>
                <h1 class="main-title">${slide.title}</h1>
                ${slide.subtitle ? `<p class="subtitle">${slide.subtitle}</p>` : ''}
                <div class="title-decoration"></div>
                <div class="title-features">
                    <div class="feature-item">
                        <i class="fas fa-chart-line"></i>
                        <span>${isArabic ? 'تحليل شامل' : 'Comprehensive Analysis'}</span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-lightbulb"></i>
                        <span>${isArabic ? 'حلول مبتكرة' : 'Innovative Solutions'}</span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-target"></i>
                        <span>${isArabic ? 'نتائج مضمونة' : 'Guaranteed Results'}</span>
                    </div>
                </div>
            </div>
            <div class="slide-footer">
                <div class="company-branding">
                    <div class="logo-section">
                        <i class="fas fa-brain"></i>
                        <span class="company-name">WIDDX AI</span>
                    </div>
                    <p class="created-by">${isArabic ? 'تم إنشاؤه بواسطة الذكاء الاصطناعي المتقدم' : 'Created by Advanced AI Technology'}</p>
                </div>
            </div>
        </div>
    </div>`
  }

  /**
   * Generate content slide HTML
   */
  private static generateContentSlide(slide: any, slideClass: string, isArabic: boolean): string {
    const contentItems = slide.content ? slide.content.map((item: string, index: number) => {
      const icons = ['fas fa-chart-bar', 'fas fa-users', 'fas fa-cogs', 'fas fa-trophy', 'fas fa-rocket', 'fas fa-star']
      const icon = icons[index % icons.length]

      return `
        <div class="content-card animate__animated animate__fadeInUp" style="animation-delay: ${index * 0.15}s">
            <div class="card-header">
                <div class="card-icon">
                    <i class="${icon}"></i>
                </div>
                <div class="card-number">${index + 1}</div>
            </div>
            <div class="card-content">
                <p class="card-text">${item}</p>
            </div>
            <div class="card-decoration"></div>
        </div>
      `
    }).join('') : ''

    return `
    <div class="${slideClass}" data-slide="${slide.type}">
        <div class="slide-background">
            <div class="gradient-overlay content-gradient"></div>
            <div class="background-pattern"></div>
            <div class="floating-elements">
                <div class="float-element float-1"></div>
                <div class="float-element float-2"></div>
                <div class="float-element float-3"></div>
            </div>
        </div>
        <div class="slide-content">
            <div class="content-header animate__animated animate__fadeInDown">
                <div class="header-icon">
                    <i class="fas fa-lightbulb"></i>
                </div>
                <h2 class="slide-title">${slide.title}</h2>
                <div class="title-underline"></div>
                <p class="slide-description">${isArabic ? 'نقاط رئيسية ومهمة للنجاح' : 'Key Points for Success'}</p>
            </div>
            <div class="content-body">
                <div class="cards-container">
                    ${contentItems}
                </div>
            </div>
            <div class="slide-footer-info">
                <div class="info-badge">
                    <i class="fas fa-info-circle"></i>
                    <span>${isArabic ? 'معلومات محدثة ودقيقة' : 'Updated & Accurate Information'}</span>
                </div>
            </div>
        </div>
    </div>`
  }

  /**
   * Generate conclusion slide HTML
   */
  private static generateConclusionSlide(slide: any, slideClass: string, isArabic: boolean): string {
    const contentItems = slide.content ? slide.content.map((item: string, index: number) => {
      const icons = ['fas fa-rocket', 'fas fa-chart-line', 'fas fa-trophy', 'fas fa-star', 'fas fa-crown', 'fas fa-gem']
      const icon = icons[index % icons.length]

      return `
        <div class="conclusion-card animate__animated animate__fadeInUp" style="animation-delay: ${index * 0.2}s">
            <div class="conclusion-icon">
                <i class="${icon}"></i>
            </div>
            <div class="conclusion-text">
                <p>${item}</p>
            </div>
            <div class="conclusion-arrow">
                <i class="fas fa-arrow-right"></i>
            </div>
        </div>
      `
    }).join('') : ''

    return `
    <div class="${slideClass}" data-slide="${slide.type}">
        <div class="slide-background">
            <div class="gradient-overlay conclusion-gradient"></div>
            <div class="success-pattern"></div>
            <div class="celebration-elements">
                <div class="confetti confetti-1"></div>
                <div class="confetti confetti-2"></div>
                <div class="confetti confetti-3"></div>
                <div class="confetti confetti-4"></div>
            </div>
        </div>
        <div class="slide-content">
            <div class="conclusion-header animate__animated animate__fadeInDown">
                <div class="success-icon">
                    <i class="fas fa-trophy"></i>
                </div>
                <h2 class="conclusion-title">${slide.title}</h2>
                <div class="title-underline golden"></div>
                <p class="slide-description">${isArabic ? 'الخطوات التالية للنجاح' : 'Next Steps to Success'}</p>
            </div>
            <div class="conclusion-body">
                <div class="conclusion-cards">
                    ${contentItems}
                </div>
            </div>
            <div class="conclusion-footer animate__animated animate__fadeIn" style="animation-delay: 1s">
                <div class="thank-you-section">
                    <div class="thank-you-icon">
                        <i class="fas fa-heart"></i>
                    </div>
                    <h3 class="thank-you-title">${isArabic ? 'شكراً لكم' : 'Thank You'}</h3>
                    <p class="thank-you-message">${isArabic ? 'نتمنى لكم التوفيق والنجاح' : 'Wishing You Success & Prosperity'}</p>
                </div>
                <div class="contact-info">
                    <div class="contact-item">
                        <i class="fas fa-globe"></i>
                        <span>WIDDX.ai</span>
                    </div>
                    <div class="contact-item">
                        <i class="fas fa-envelope"></i>
                        <span><EMAIL></span>
                    </div>
                </div>
            </div>
        </div>
    </div>`
  }

  /**
   * Get advanced CSS styles
   */
  static getAdvancedCSS(isArabic: boolean): string {
    return `
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: ${isArabic ? "'Tajawal', sans-serif" : "'Inter', sans-serif"};
            background: #0f0f23;
            color: #ffffff;
            overflow: hidden;
            direction: ${isArabic ? 'rtl' : 'ltr'};
        }

        .presentation-container {
            width: 100vw;
            height: 100vh;
            position: relative;
        }

        .slide {
            width: 100%;
            height: 100vh;
            position: absolute;
            top: 0;
            left: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .slide.active {
            opacity: 1;
            transform: translateX(0);
        }

        .slide.prev {
            transform: translateX(-100%);
        }

        .slide-background {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
        }

        .gradient-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            opacity: 0.9;
        }

        .conclusion-gradient {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%) !important;
        }

        .geometric-shapes {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
        }

        .shape {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            animation: float 6s ease-in-out infinite;
        }

        .shape-1 {
            width: 200px;
            height: 200px;
            top: 20%;
            left: 10%;
            animation-delay: 0s;
        }

        .shape-2 {
            width: 150px;
            height: 150px;
            top: 60%;
            right: 15%;
            animation-delay: 2s;
        }

        .shape-3 {
            width: 100px;
            height: 100px;
            top: 80%;
            left: 70%;
            animation-delay: 4s;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        .slide-content {
            position: relative;
            z-index: 2;
            max-width: 1200px;
            width: 90%;
            text-align: center;
            padding: 2rem;
        }

        .main-title {
            font-size: clamp(2.5rem, 6vw, 4.5rem);
            font-weight: 900;
            margin-bottom: 1.5rem;
            background: linear-gradient(135deg, #ffffff, #f0f9ff);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            text-shadow: 0 0 30px rgba(255, 255, 255, 0.5);
            line-height: 1.2;
        }

        .subtitle {
            font-size: clamp(1.2rem, 3vw, 2rem);
            font-weight: 400;
            opacity: 0.9;
            margin-bottom: 2rem;
        }

        .title-decoration {
            width: 150px;
            height: 4px;
            background: linear-gradient(90deg, #ff9d45, #ff6b35);
            margin: 2rem auto;
            border-radius: 2px;
            position: relative;
        }

        .title-decoration::after {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            border-radius: 4px;
            animation: shimmer 2s ease-in-out infinite;
        }

        @keyframes shimmer {
            0% { opacity: 0; }
            50% { opacity: 1; }
            100% { opacity: 0; }
        }

        .slide-title {
            font-size: clamp(2rem, 4vw, 3.5rem);
            font-weight: 800;
            margin-bottom: 1rem;
            color: #ffffff;
        }

        .slide-subtitle {
            font-size: clamp(1rem, 2vw, 1.5rem);
            opacity: 0.8;
            margin-bottom: 2rem;
        }

        .content-list, .conclusion-list {
            list-style: none;
            text-align: ${isArabic ? 'right' : 'left'};
            max-width: 800px;
            margin: 0 auto;
        }

        .content-item, .conclusion-item {
            display: flex;
            align-items: center;
            margin-bottom: 1.5rem;
            font-size: clamp(1rem, 2vw, 1.3rem);
            line-height: 1.6;
            padding: 1rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .content-item:hover, .conclusion-item:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .item-icon {
            font-size: 1.2rem;
            margin-${isArabic ? 'left' : 'right'}: 1rem;
            color: #4ade80;
            flex-shrink: 0;
        }

        .conclusion-item .item-icon {
            color: #fbbf24;
        }

        .navigation {
            position: fixed;
            bottom: 2rem;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            align-items: center;
            gap: 1rem;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            padding: 1rem 2rem;
            border-radius: 50px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            z-index: 1000;
        }

        .nav-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            font-size: 1.2rem;
        }

        .nav-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }

        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .slide-counter {
            color: white;
            font-weight: 600;
            font-size: 1.1rem;
            margin: 0 1rem;
        }

        .progress-bar {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: rgba(255, 255, 255, 0.2);
            z-index: 1000;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4ade80, #22d3ee);
            transition: width 0.8s ease;
            width: 0%;
        }

        .slide-footer {
            position: absolute;
            bottom: 2rem;
            right: 2rem;
            opacity: 0.7;
            font-size: 0.9rem;
        }

        .conclusion-footer {
            margin-top: 3rem;
        }

        .thank-you {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            font-size: 1.5rem;
            font-weight: 600;
            color: #fbbf24;
        }

        .thank-you i {
            color: #ef4444;
            animation: heartbeat 2s ease-in-out infinite;
        }

        @keyframes heartbeat {
            0% { transform: scale(1); }
            50% { transform: scale(1.2); }
            100% { transform: scale(1); }
        }

        /* Advanced Card Styles */
        .cards-container, .conclusion-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin: 2rem 0;
            max-width: 1200px;
            margin-left: auto;
            margin-right: auto;
        }

        .content-card, .conclusion-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 2rem;
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.4s ease;
            position: relative;
            overflow: hidden;
        }

        .content-card:hover, .conclusion-card:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-10px) scale(1.02);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }

        .card-icon {
            font-size: 2.5rem;
            color: #4facfe;
            text-shadow: 0 2px 10px rgba(79, 172, 254, 0.5);
        }

        .card-number {
            background: linear-gradient(135deg, #4facfe, #00f2fe);
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 1.2rem;
        }

        .card-text {
            font-size: 1.1rem;
            line-height: 1.6;
            color: rgba(255, 255, 255, 0.9);
            margin: 0;
        }

        .card-decoration {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #4facfe, #00f2fe);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .content-card:hover .card-decoration,
        .conclusion-card:hover .card-decoration {
            transform: scaleX(1);
        }

        /* Header Enhancements */
        .header-icon, .success-icon {
            font-size: 3rem;
            color: #4facfe;
            margin-bottom: 1rem;
            text-shadow: 0 4px 15px rgba(79, 172, 254, 0.5);
            animation: pulse 2s infinite;
        }

        .slide-description {
            font-size: 1.2rem;
            color: rgba(255, 255, 255, 0.8);
            margin-top: 1rem;
            font-style: italic;
        }

        .title-underline {
            width: 80px;
            height: 4px;
            background: linear-gradient(90deg, #4facfe, #00f2fe);
            margin: 1rem auto;
            border-radius: 2px;
        }

        .title-underline.golden {
            background: linear-gradient(90deg, #ffd700, #ffed4e);
        }

        /* Background Patterns */
        .content-gradient {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .background-pattern {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="2" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100%" height="100%" fill="url(%23dots)"/></svg>');
            opacity: 0.3;
        }

        .floating-elements {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            overflow: hidden;
        }

        .float-element {
            position: absolute;
            width: 60px;
            height: 60px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            animation: float 8s ease-in-out infinite;
        }

        .float-1 {
            top: 20%;
            left: 10%;
            animation-delay: 0s;
        }

        .float-2 {
            top: 60%;
            right: 15%;
            animation-delay: 2s;
        }

        .float-3 {
            bottom: 30%;
            left: 20%;
            animation-delay: 4s;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        /* Conclusion Enhancements */
        .success-pattern {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="stars" width="25" height="25" patternUnits="userSpaceOnUse"><polygon points="12.5,2 15.5,8.5 22.5,8.5 17,13 18.5,20 12.5,16 6.5,20 8,13 2.5,8.5 9.5,8.5" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100%" height="100%" fill="url(%23stars)"/></svg>');
            opacity: 0.2;
        }

        .celebration-elements {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            overflow: hidden;
        }

        .confetti {
            position: absolute;
            width: 10px;
            height: 10px;
            background: #ffd700;
            animation: confetti-fall 3s linear infinite;
        }

        .confetti-1 {
            left: 20%;
            animation-delay: 0s;
            background: #ff6b6b;
        }

        .confetti-2 {
            left: 40%;
            animation-delay: 0.5s;
            background: #4ecdc4;
        }

        .confetti-3 {
            left: 60%;
            animation-delay: 1s;
            background: #45b7d1;
        }

        .confetti-4 {
            left: 80%;
            animation-delay: 1.5s;
            background: #96ceb4;
        }

        .conclusion-icon {
            font-size: 2rem;
            color: #ffd700;
            margin-bottom: 1rem;
            text-shadow: 0 2px 10px rgba(255, 215, 0, 0.5);
        }

        .conclusion-text p {
            font-size: 1.1rem;
            line-height: 1.6;
            color: rgba(255, 255, 255, 0.9);
            margin: 0;
        }

        .conclusion-arrow {
            margin-top: 1rem;
            color: #4facfe;
            font-size: 1.5rem;
            text-align: center;
        }

        .thank-you-section {
            text-align: center;
            margin-bottom: 2rem;
        }

        .thank-you-icon {
            font-size: 3rem;
            color: #ff6b6b;
            margin-bottom: 1rem;
            animation: heartbeat 2s ease-in-out infinite;
        }

        .thank-you-title {
            font-size: 2.5rem;
            font-weight: bold;
            color: white;
            margin-bottom: 0.5rem;
        }

        .thank-you-message {
            font-size: 1.2rem;
            color: rgba(255, 255, 255, 0.8);
            margin: 0;
        }

        .contact-info {
            display: flex;
            justify-content: center;
            gap: 2rem;
            flex-wrap: wrap;
        }

        .contact-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            background: rgba(255, 255, 255, 0.1);
            padding: 0.8rem 1.5rem;
            border-radius: 25px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .contact-item i {
            color: #4facfe;
            font-size: 1.2rem;
        }

        .contact-item span {
            color: white;
            font-weight: 500;
        }

        .info-badge {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            background: rgba(79, 172, 254, 0.2);
            padding: 0.8rem 1.5rem;
            border-radius: 25px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(79, 172, 254, 0.3);
            margin: 2rem auto 0;
            max-width: 300px;
        }

        .info-badge i {
            color: #4facfe;
            font-size: 1.1rem;
        }

        .info-badge span {
            color: rgba(255, 255, 255, 0.9);
            font-size: 0.9rem;
            font-weight: 500;
        }

        @keyframes confetti-fall {
            0% { transform: translateY(-100vh) rotate(0deg); opacity: 1; }
            100% { transform: translateY(100vh) rotate(360deg); opacity: 0; }
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .slide-content {
                width: 95%;
                padding: 1rem;
            }

            .navigation {
                bottom: 1rem;
                padding: 0.8rem 1.5rem;
            }

            .nav-btn {
                width: 40px;
                height: 40px;
                font-size: 1rem;
            }

            .cards-container, .conclusion-cards {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }

            .content-card, .conclusion-card {
                padding: 1.5rem;
            }

            .contact-info {
                flex-direction: column;
                gap: 1rem;
            }

            .thank-you-title {
                font-size: 2rem;
            }
        }

        /* Print Styles */
        @media print {
            .slide {
                position: static !important;
                opacity: 1 !important;
                transform: none !important;
                page-break-after: always;
                height: auto;
                min-height: 100vh;
            }
            
            .navigation, .progress-bar {
                display: none !important;
            }
        }
    `
  }

  /**
   * Get advanced JavaScript functionality
   */
  static getAdvancedJavaScript(): string {
    return `
        let currentSlideIndex = 0;
        let totalSlides = 0;
        let slides = [];

        // Initialize presentation
        document.addEventListener('DOMContentLoaded', function() {
            slides = document.querySelectorAll('.slide');
            totalSlides = slides.length;
            
            if (totalSlides > 0) {
                showSlide(0);
                updateProgress();
                
                // Add animation classes
                slides.forEach((slide, index) => {
                    if (index === 0) {
                        slide.classList.add('active');
                    }
                });
            }
            
            // Keyboard navigation
            document.addEventListener('keydown', handleKeyPress);
            
            // Touch/swipe support
            let touchStartX = 0;
            let touchEndX = 0;
            
            document.addEventListener('touchstart', function(e) {
                touchStartX = e.changedTouches[0].screenX;
            });
            
            document.addEventListener('touchend', function(e) {
                touchEndX = e.changedTouches[0].screenX;
                handleSwipe();
            });
            
            function handleSwipe() {
                const swipeThreshold = 50;
                const diff = touchStartX - touchEndX;
                
                if (Math.abs(diff) > swipeThreshold) {
                    if (diff > 0) {
                        nextSlide();
                    } else {
                        previousSlide();
                    }
                }
            }
        });

        function showSlide(index) {
            if (index < 0 || index >= totalSlides) return;
            
            slides.forEach((slide, i) => {
                slide.classList.remove('active', 'prev');
                if (i === index) {
                    slide.classList.add('active');
                } else if (i < index) {
                    slide.classList.add('prev');
                }
            });
            
            currentSlideIndex = index;
            updateNavigation();
            updateProgress();
            
            // Trigger animations
            const activeSlide = slides[index];
            const animatedElements = activeSlide.querySelectorAll('.animate__animated');
            animatedElements.forEach((element, i) => {
                element.style.animationDelay = (i * 0.1) + 's';
                element.classList.add('animate__fadeInUp');
            });
        }

        function nextSlide() {
            if (currentSlideIndex < totalSlides - 1) {
                showSlide(currentSlideIndex + 1);
            }
        }

        function previousSlide() {
            if (currentSlideIndex > 0) {
                showSlide(currentSlideIndex - 1);
            }
        }

        function updateNavigation() {
            const prevBtn = document.getElementById('prevBtn');
            const nextBtn = document.getElementById('nextBtn');
            const currentSlideSpan = document.getElementById('currentSlide');
            
            if (prevBtn) prevBtn.disabled = currentSlideIndex === 0;
            if (nextBtn) nextBtn.disabled = currentSlideIndex === totalSlides - 1;
            if (currentSlideSpan) currentSlideSpan.textContent = currentSlideIndex + 1;
        }

        function updateProgress() {
            const progressFill = document.getElementById('progressFill');
            if (progressFill && totalSlides > 0) {
                const progress = ((currentSlideIndex + 1) / totalSlides) * 100;
                progressFill.style.width = progress + '%';
            }
        }

        function handleKeyPress(e) {
            switch(e.key) {
                case 'ArrowRight':
                case ' ':
                    e.preventDefault();
                    nextSlide();
                    break;
                case 'ArrowLeft':
                    e.preventDefault();
                    previousSlide();
                    break;
                case 'Home':
                    e.preventDefault();
                    showSlide(0);
                    break;
                case 'End':
                    e.preventDefault();
                    showSlide(totalSlides - 1);
                    break;
                case 'f':
                case 'F11':
                    e.preventDefault();
                    toggleFullscreen();
                    break;
                case 'Escape':
                    if (document.fullscreenElement) {
                        document.exitFullscreen();
                    }
                    break;
            }
        }

        function toggleFullscreen() {
            if (!document.fullscreenElement) {
                document.documentElement.requestFullscreen().catch(err => {
                    console.log('Error attempting to enable fullscreen:', err.message);
                });
            } else {
                document.exitFullscreen();
            }
        }

        // Auto-play functionality (optional)
        let autoPlayInterval;
        
        function startAutoPlay(intervalMs = 10000) {
            stopAutoPlay();
            autoPlayInterval = setInterval(() => {
                if (currentSlideIndex < totalSlides - 1) {
                    nextSlide();
                } else {
                    showSlide(0); // Loop back to start
                }
            }, intervalMs);
        }
        
        function stopAutoPlay() {
            if (autoPlayInterval) {
                clearInterval(autoPlayInterval);
                autoPlayInterval = null;
            }
        }

        // Pause auto-play on user interaction
        document.addEventListener('click', stopAutoPlay);
        document.addEventListener('keydown', stopAutoPlay);
        document.addEventListener('touchstart', stopAutoPlay);

        console.log('🎉 WIDDX AI Advanced Presentation System loaded successfully!');
    `
  }
}
