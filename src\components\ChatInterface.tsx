'use client'

import { useState, useEffect } from 'react'
import { useTheme } from '@/hooks/useTheme'
import { Header } from './Header'
import { Sidebar } from './Sidebar'
import { ChatArea } from './ChatArea'
import { SlidePanel } from './SlidePanel'
import { RTLWrapper } from './RTLWrapper'
import MCPThinkingProcess from './MCPThinkingProcess'
import { useChat } from '@/hooks/useChat'
import type { Locale, ChatMode } from '@/types'

interface ChatInterfaceProps {
  locale: Locale
  onLocaleChange: (locale: Locale) => void
}

export function ChatInterface({ locale, onLocaleChange }: ChatInterfaceProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const [currentMode, setCurrentMode] = useState<ChatMode>('chat')
  const [slidePanelOpen, setSlidePanelOpen] = useState(false)
  const [slideContent, setSlideContent] = useState('')
  const [slideTitle, setSlideTitle] = useState('')
  const [showMCPThinking, setShowMCPThinking] = useState(false)
  const [thinkingTopic, setThinkingTopic] = useState('')
  const { theme, setTheme } = useTheme()
  const [mounted, setMounted] = useState(false)
  
  const {
    chats,
    currentChat,
    isLoading,
    isTyping,
    createNewChat,
    selectChat,
    deleteChat,
    sendMessage,
    regenerateLastResponse,
    clearChat,
  } = useChat(locale)

  // Handle hydration
  useEffect(() => {
    setMounted(true)
  }, [])

  // Handle keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Cmd/Ctrl + K: New chat
      if ((e.metaKey || e.ctrlKey) && e.key === 'k') {
        e.preventDefault()
        createNewChat(currentMode)
      }
      // Escape: Close sidebar/panel
      if (e.key === 'Escape') {
        if (slidePanelOpen) {
          setSlidePanelOpen(false)
        } else if (sidebarOpen) {
          setSidebarOpen(false)
        }
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [createNewChat, currentMode, sidebarOpen, slidePanelOpen])

  const handleToggleSidebar = () => {
    setSidebarOpen(!sidebarOpen)
  }

  const handleToggleTheme = () => {
    setTheme()
  }

  const handleToggleLocale = () => {
    onLocaleChange(locale === 'en' ? 'ar' : 'en')
  }

  const handleModeChange = (mode: ChatMode) => {
    setCurrentMode(mode)
    createNewChat(mode)
  }

  const handleSendMessage = async (content: string) => {
    // For slides mode, show MCP thinking process
    if (currentMode === 'slides') {
      setThinkingTopic(content)
      setShowMCPThinking(true)
    }

    // Send message for all modes
    await sendMessage(content)
  }

  const handleOpenSlidePanel = (content: string, title: string = 'Presentation') => {
    setSlideContent(content)
    setSlideTitle(title)
    setSlidePanelOpen(true)
  }

  const handleCloseSlidePanel = () => {
    setSlidePanelOpen(false)
    setSlideContent('')
    setSlideTitle('')
  }

  const handleMCPThinkingComplete = (result: any) => {
    setShowMCPThinking(false)
    // The result contains MCP enhancement data
    if (result && result.success) {
      console.log('🎉 MCP thinking process completed:', result)
      // The actual presentation will be generated by the AI manager
    }
    setThinkingTopic('')
  }

  const handleNewChat = () => {
    createNewChat(currentMode)
    // Close sidebar on mobile
    if (window.innerWidth < 768) {
      setSidebarOpen(false)
    }
  }

  if (!mounted) {
    return null
  }

  return (
    <RTLWrapper locale={locale}>
      <div className="h-screen flex flex-col bg-white dark:bg-gray-900 text-gray-900 dark:text-white overflow-hidden">
        {/* Fixed Header */}
        <Header
          onToggleSidebar={handleToggleSidebar}
          onToggleTheme={handleToggleTheme}
          onToggleLocale={handleToggleLocale}
          onNewChat={handleNewChat}
          currentMode={currentMode}
          onModeChange={handleModeChange}
          locale={locale}
        />

        {/* Main Layout Container */}
        <div className="flex-1 flex overflow-hidden relative">
          {/* Sidebar */}
          <Sidebar
            isOpen={sidebarOpen}
            onToggle={() => setSidebarOpen(false)}
            chats={chats}
            currentChat={currentChat}
            onSelectChat={selectChat}
            onDeleteChat={deleteChat}
            onNewChat={handleNewChat}
            locale={locale}
          />

          {/* Mobile Sidebar Backdrop */}
          {sidebarOpen && (
            <div
              className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
              onClick={() => setSidebarOpen(false)}
            />
          )}

          {/* Content Area */}
          <div className="flex-1 flex overflow-hidden">
            {/* Chat Area */}
            <div className={`transition-all duration-300 ease-in-out flex flex-col overflow-hidden ${
              slidePanelOpen
                ? 'w-full lg:w-1/2' // Full width on mobile, half on desktop
                : 'w-full'
            }`}>
              <ChatArea
                currentChat={currentChat}
                currentMode={currentMode}
                isLoading={isLoading}
                isTyping={isTyping}
                onSendMessage={handleSendMessage}
                onRegenerateResponse={regenerateLastResponse}
                onClearChat={clearChat}
                onModeSelect={handleModeChange}
                onOpenSlidePanel={handleOpenSlidePanel}
                locale={locale}
              />
            </div>

            {/* Slide Panel */}
            <SlidePanel
              isOpen={slidePanelOpen}
              onClose={handleCloseSlidePanel}
              content={slideContent}
              title={slideTitle}
              locale={locale}
            />
          </div>

          {/* Mobile Slide Panel Backdrop */}
          {slidePanelOpen && (
            <div
              className="fixed inset-0 bg-black bg-opacity-50 z-50 lg:hidden"
              onClick={handleCloseSlidePanel}
            />
          )}
        </div>

        {/* MCP Thinking Process */}
        <MCPThinkingProcess
          topic={thinkingTopic}
          isVisible={showMCPThinking}
          locale={locale}
          onComplete={handleMCPThinkingComplete}
        />
      </div>
    </RTLWrapper>
  )
}
