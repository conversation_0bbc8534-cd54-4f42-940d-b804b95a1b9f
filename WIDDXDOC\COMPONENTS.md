# Components Documentation

## Component Hierarchy

```mermaid
graph TD
    A[ChatInterface] --> B[Header]
    A --> C[Sidebar]
    A --> D[ChatArea]
    A --> E[SlidePanel]
    D --> F[MessageList]
    D --> G[ChatInput]
    F --> H[Message]
    E --> I[EnhancedSlideViewer]
    E --> J[TemplateSelector]
    D --> K[WelcomeScreen]
    H --> L[TypingIndicator]
    A --> M[RTLWrapper]
```

## Core Components

### ChatInterface (src/components/ChatInterface.tsx)
- Main container component for the chat application
- Manages application state and coordinates between other components
- Handles locale changes and mode switching

### ChatArea (src/components/ChatArea.tsx)
- Container for chat messages and input
- Displays WelcomeScreen when no messages exist
- Contains MessageList and ChatInput components

### MessageList (src/components/MessageList.tsx)
- Renders a list of Message components
- Handles animations for message transitions

### Message (src/components/Message.tsx)
- Displays individual chat messages
- Supports markdown rendering
- Handles message actions (copy, regenerate)

### ChatInput (src/components/ChatInput.tsx)
- Text input area for user messages
- Handles message submission
- Supports file uploads and voice recording

### SlidePanel (src/components/SlidePanel.tsx)
- Displays generated slides
- Contains EnhancedSlideViewer for slide presentation
- Provides slide export options

### EnhancedSlideViewer (src/components/EnhancedSlideViewer.tsx)
- Advanced slide presentation component
- Supports animations, fullscreen mode, and keyboard controls
- Handles slide navigation

## UI Components

### Header (src/components/Header.tsx)
- Application header with title and mode selector
- Displays current chat mode

### Sidebar (src/components/Sidebar.tsx)
- Shows chat history
- Allows navigation between chats
- Provides chat deletion functionality

### WelcomeScreen (src/components/WelcomeScreen.tsx)
- Displays when no messages exist
- Shows features and quick start suggestions

### SplashScreen (src/components/SplashScreen.tsx)
- Loading screen with progress indicator
- Displays during initial load

### TypingIndicator (src/components/TypingIndicator.tsx)
- Animated typing indicator
- Shows when AI is generating a response

## Utility Components

### RTLWrapper (src/components/RTLWrapper.tsx)
- Handles RTL (Right-to-Left) layout for Arabic locale
- Wraps child components and applies RTL styles