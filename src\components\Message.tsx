'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import ReactMarkdown from 'react-markdown'
import {
  <PERSON><PERSON>,
  <PERSON>otateCcw,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Check,
  Clock,
  Presentation
} from 'lucide-react'
import { t } from '@/locales'
import type { MessageProps, Locale } from '@/types'

interface ExtendedMessageProps extends MessageProps {
  onOpenSlidePanel?: (content: string, title?: string) => void
  locale: Locale
}

export function Message({
  message,
  isLast,
  onRegenerate,
  onOpenSlidePanel,
  locale,
}: ExtendedMessageProps) {
  const [copied, setCopied] = useState(false)
  const [isRegenerating, setIsRegenerating] = useState(false)

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(message.content)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (error) {
      console.error('Failed to copy text:', error)
    }
  }

  const handleRegenerate = async () => {
    if (!onRegenerate || isRegenerating) return

    setIsRegenerating(true)
    try {
      await onRegenerate()
    } finally {
      setIsRegenerating(false)
    }
  }

  // Check if message contains slide data
  const hasSlideData = () => {
    return message.metadata?.slideData?.html && message.metadata?.slideData?.title
  }

  const handleOpenSlideViewer = () => {
    if (onOpenSlidePanel && message.metadata?.slideData) {
      onOpenSlidePanel(message.metadata.slideData.html, message.metadata.slideData.title)
    }
  }

  const formatTime = (date: Date) => {
    return new Intl.DateTimeFormat(locale === 'ar' ? 'ar-SA' : 'en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: locale === 'en',
    }).format(date)
  }

  const isUser = message.role === 'user'
  const isAssistant = message.role === 'assistant'

  return (
    <>
    <div className={`flex gap-4 ${isUser ? 'flex-row-reverse' : 'flex-row'}`}>
      {/* Avatar */}
      <div className="flex-shrink-0">
        <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
          isUser
            ? 'bg-light-accent dark:bg-dark-accent text-white'
            : 'bg-light-secondary dark:bg-dark-secondary border border-light-border dark:border-dark-border text-light-text-primary dark:text-dark-text-primary'
        }`}>
          {isUser ? (
            <User className="w-5 h-5" />
          ) : (
            <Bot className="w-5 h-5" />
          )}
        </div>
      </div>

      {/* Message Content */}
      <div className={`flex-1 max-w-none chat-message ${isUser ? 'max-w-[80%]' : ''}`}>
        <div className={`group relative ${
          isUser
            ? 'bg-light-accent dark:bg-dark-accent text-white rounded-2xl rounded-tr-md px-4 py-3'
            : 'bg-light-secondary dark:bg-dark-secondary border border-light-border dark:border-dark-border rounded-2xl rounded-tl-md px-4 py-3'
        }`}>
          {/* Message Text */}
          <div className={`prose prose-sm max-w-none message-content ${
            isUser
              ? 'prose-invert text-white'
              : 'prose-gray dark:prose-invert text-light-text-primary dark:text-dark-text-primary'
          }`}>
            {isAssistant ? (
              <ReactMarkdown
                components={{
                  // Custom components for better styling
                  p: ({ children }) => <p className="mb-3 last:mb-0">{children}</p>,
                  h1: ({ children }) => <h1 className="text-lg font-bold mb-2 mt-4 first:mt-0">{children}</h1>,
                  h2: ({ children }) => <h2 className="text-base font-bold mb-2 mt-3 first:mt-0">{children}</h2>,
                  h3: ({ children }) => <h3 className="text-sm font-bold mb-2 mt-3 first:mt-0">{children}</h3>,
                  ul: ({ children }) => <ul className="list-disc list-inside mb-3 space-y-1">{children}</ul>,
                  ol: ({ children }) => <ol className="list-decimal list-inside mb-3 space-y-1">{children}</ol>,
                  li: ({ children }) => <li className="text-sm">{children}</li>,
                  code: ({ children, className }) => {
                    const isInline = !className
                    return isInline ? (
                      <code className="bg-light-tertiary dark:bg-dark-tertiary px-1.5 py-0.5 rounded text-xs font-mono word-wrap break-word overflow-wrap break-word">
                        {children}
                      </code>
                    ) : (
                      <code className="block bg-light-tertiary dark:bg-dark-tertiary p-3 rounded-lg text-xs font-mono overflow-x-auto word-wrap break-word overflow-wrap break-word white-space pre-wrap max-width-full">
                        {children}
                      </code>
                    )
                  },
                  pre: ({ children }) => (
                    <pre className="bg-light-tertiary dark:bg-dark-tertiary p-3 rounded-lg overflow-x-auto mb-3 word-wrap break-word overflow-wrap break-word white-space pre-wrap max-width-full">
                      {children}
                    </pre>
                  ),
                  blockquote: ({ children }) => (
                    <blockquote className="border-l-4 border-light-accent dark:border-dark-accent pl-4 italic mb-3">
                      {children}
                    </blockquote>
                  ),
                  table: ({ children }) => (
                    <div className="overflow-x-auto mb-3">
                      <table className="min-w-full border border-light-border dark:border-dark-border rounded-lg">
                        {children}
                      </table>
                    </div>
                  ),
                  th: ({ children }) => (
                    <th className="border border-light-border dark:border-dark-border px-3 py-2 bg-light-tertiary dark:bg-dark-tertiary font-semibold text-left">
                      {children}
                    </th>
                  ),
                  td: ({ children }) => (
                    <td className="border border-light-border dark:border-dark-border px-3 py-2">
                      {children}
                    </td>
                  ),
                }}
              >
                {message.content}
              </ReactMarkdown>
            ) : (
              <div className="whitespace-pre-wrap break-words text-content word-wrap break-word overflow-wrap break-word max-width-full">
                {message.content}
              </div>
            )}
          </div>

          {/* Message Actions */}
          <div className={`flex items-center justify-between mt-3 pt-2 border-t ${
            isUser
              ? 'border-white/20'
              : 'border-light-border dark:border-dark-border'
          }`}>
            {/* Timestamp */}
            <div className={`flex items-center gap-1 text-xs ${
              isUser
                ? 'text-white/70'
                : 'text-light-text-muted dark:text-dark-text-muted'
            }`}>
              <Clock className="w-3 h-3" />
              <span>{formatTime(message.timestamp)}</span>
              {message.metadata?.processingTime && (
                <span className="ml-2">
                  ({Math.round(message.metadata.processingTime)}ms)
                </span>
              )}
            </div>

            {/* Action Buttons */}
            <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
              {/* Slide Viewer Button (only for messages with slide data) */}
              {isAssistant && hasSlideData() && (
                <button
                  onClick={handleOpenSlideViewer}
                  className="flex items-center gap-2 px-3 py-2 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-xl transition-all duration-200 hover:from-blue-600 hover:to-purple-700 hover:shadow-lg hover:scale-105 font-medium text-sm"
                  aria-label={locale === 'ar' ? 'عرض العرض التقديمي' : 'Show Presentation'}
                  title={locale === 'ar' ? 'عرض العرض التقديمي التفاعلي' : 'Open Interactive Presentation'}
                >
                  <Presentation className="w-4 h-4" />
                  <span>{locale === 'ar' ? 'عرض العرض التقديمي' : 'Show Presentation'}</span>
                </button>
              )}

              {/* Copy Button */}
              <button
                onClick={handleCopy}
                className={`p-1.5 rounded-md transition-colors ${
                  isUser
                    ? 'hover:bg-white/20 text-white/70 hover:text-white'
                    : 'hover:bg-light-tertiary dark:hover:bg-dark-tertiary text-light-text-muted dark:text-dark-text-muted hover:text-light-text-primary dark:hover:text-dark-text-primary'
                }`}
                aria-label={t(locale, 'a11y.copyMessage')}
              >
                {copied ? (
                  <Check className="w-3 h-3" />
                ) : (
                  <Copy className="w-3 h-3" />
                )}
              </button>

              {/* Regenerate Button (only for last assistant message) */}
              {isAssistant && isLast && onRegenerate && (
                <button
                  onClick={handleRegenerate}
                  disabled={isRegenerating}
                  className="p-1.5 rounded-md transition-colors hover:bg-light-tertiary dark:hover:bg-dark-tertiary text-light-text-muted dark:text-dark-text-muted hover:text-light-text-primary dark:hover:text-dark-text-primary disabled:opacity-50"
                  aria-label={t(locale, 'a11y.regenerateResponse')}
                >
                  <RotateCcw className={`w-3 h-3 ${isRegenerating ? 'animate-spin' : ''}`} />
                </button>
              )}
            </div>
          </div>
        </div>

        {/* Model Info (for assistant messages) */}
        {isAssistant && message.metadata && (
          <div className="mt-2 text-xs text-light-text-muted dark:text-dark-text-muted">
            <span>{message.metadata.model}</span>
            {message.metadata.tokens && (
              <span className="ml-2">• {message.metadata.tokens} tokens</span>
            )}
          </div>
        )}
      </div>
    </div>
  </>
  )
}