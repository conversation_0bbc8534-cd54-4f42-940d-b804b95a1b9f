/**
 * MCP Service Manager - Central hub for managing Model Context Protocol services
 * Integrates free MCP services to enhance the WIDDX AI presentation system
 */

interface MCPService {
  name: string;
  endpoint: string;
  enabled: boolean;
  capabilities: string[];
}

interface MCPResponse {
  success: boolean;
  data?: any;
  error?: string;
  source: string;
}

interface SearchResult {
  title: string;
  url: string;
  snippet: string;
  relevance?: number;
}

interface ChartConfig {
  type: 'bar' | 'line' | 'pie' | 'doughnut' | 'scatter';
  data: any;
  options?: any;
}

class MCPServiceManager {
  private services: Map<string, MCPService> = new Map();
  private cache: Map<string, { data: any; timestamp: number; ttl: number }> = new Map();
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes

  constructor() {
    this.initializeServices();
  }

  private initializeServices() {
    // Initialize free MCP services
    const services: MCPService[] = [
      {
        name: 'brave-search',
        endpoint: 'https://api.search.brave.com/res/v1/web/search',
        enabled: true,
        capabilities: ['web-search', 'fact-checking', 'research']
      },
      {
        name: 'quickchart',
        endpoint: 'https://quickchart.io/chart',
        enabled: true,
        capabilities: ['chart-generation', 'data-visualization']
      },
      {
        name: 'time-service',
        endpoint: 'internal',
        enabled: true,
        capabilities: ['timezone-conversion', 'date-formatting']
      },
      {
        name: 'content-fetch',
        endpoint: 'internal',
        enabled: true,
        capabilities: ['web-content-fetch', 'html-extraction']
      }
    ];

    services.forEach(service => {
      this.services.set(service.name, service);
    });

    console.log('🔗 MCP Service Manager initialized with', services.length, 'services');
  }

  /**
   * Search the web using Brave Search API (free tier)
   */
  async searchWeb(query: string, options: { 
    count?: number; 
    market?: string; 
    safesearch?: string;
  } = {}): Promise<MCPResponse> {
    const cacheKey = `search:${query}:${JSON.stringify(options)}`;
    const cached = this.getFromCache(cacheKey);
    if (cached) {
      return { success: true, data: cached, source: 'brave-search-cache' };
    }

    try {
      // For demo purposes, we'll simulate Brave Search API response
      // In production, you would use actual Brave Search API with your API key
      const mockResults: SearchResult[] = [
        {
          title: `Research results for: ${query}`,
          url: `https://example.com/search?q=${encodeURIComponent(query)}`,
          snippet: `Comprehensive information about ${query} including latest updates, statistics, and expert analysis.`,
          relevance: 0.95
        },
        {
          title: `${query} - Latest News and Updates`,
          url: `https://news.example.com/${query.replace(/\s+/g, '-')}`,
          snippet: `Recent developments and news related to ${query} from reliable sources.`,
          relevance: 0.88
        },
        {
          title: `Expert Analysis: ${query}`,
          url: `https://research.example.com/analysis/${query.replace(/\s+/g, '-')}`,
          snippet: `In-depth analysis and expert opinions on ${query} with data-driven insights.`,
          relevance: 0.82
        }
      ];

      this.setCache(cacheKey, mockResults);
      
      return {
        success: true,
        data: mockResults,
        source: 'brave-search'
      };
    } catch (error) {
      console.error('❌ Brave Search API error:', error);
      return {
        success: false,
        error: 'Failed to search web content',
        source: 'brave-search'
      };
    }
  }

  /**
   * Generate charts using QuickChart API (free tier)
   */
  async generateChart(config: ChartConfig): Promise<MCPResponse> {
    const cacheKey = `chart:${JSON.stringify(config)}`;
    const cached = this.getFromCache(cacheKey);
    if (cached) {
      return { success: true, data: cached, source: 'quickchart-cache' };
    }

    try {
      const chartConfig = {
        type: config.type,
        data: config.data,
        options: {
          responsive: true,
          plugins: {
            legend: {
              position: 'top',
            },
            title: {
              display: true,
              text: config.data.title || 'Chart'
            }
          },
          ...config.options
        }
      };

      // Generate QuickChart URL
      const chartUrl = `https://quickchart.io/chart?c=${encodeURIComponent(JSON.stringify(chartConfig))}`;
      
      const result = {
        url: chartUrl,
        config: chartConfig,
        type: config.type
      };

      this.setCache(cacheKey, result);

      return {
        success: true,
        data: result,
        source: 'quickchart'
      };
    } catch (error) {
      console.error('❌ QuickChart API error:', error);
      return {
        success: false,
        error: 'Failed to generate chart',
        source: 'quickchart'
      };
    }
  }

  /**
   * Format time and handle timezone conversions
   */
  async formatTime(options: {
    timestamp?: number;
    timezone?: string;
    format?: string;
    locale?: string;
  } = {}): Promise<MCPResponse> {
    try {
      const {
        timestamp = Date.now(),
        timezone = 'UTC',
        format = 'YYYY-MM-DD HH:mm:ss',
        locale = 'en-US'
      } = options;

      const date = new Date(timestamp);
      
      // Format date based on locale and timezone
      const formatted = new Intl.DateTimeFormat(locale, {
        timeZone: timezone,
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      }).format(date);

      const result = {
        formatted,
        timestamp,
        timezone,
        locale,
        iso: date.toISOString()
      };

      return {
        success: true,
        data: result,
        source: 'time-service'
      };
    } catch (error) {
      console.error('❌ Time service error:', error);
      return {
        success: false,
        error: 'Failed to format time',
        source: 'time-service'
      };
    }
  }

  /**
   * Fetch and extract content from web pages
   */
  async fetchWebContent(url: string): Promise<MCPResponse> {
    const cacheKey = `fetch:${url}`;
    const cached = this.getFromCache(cacheKey);
    if (cached) {
      return { success: true, data: cached, source: 'content-fetch-cache' };
    }

    try {
      // For demo purposes, simulate content fetching
      // In production, you would use actual web scraping or content extraction
      const mockContent = {
        url,
        title: `Content from ${new URL(url).hostname}`,
        text: `This is extracted content from ${url}. The content includes relevant information that can be used to enhance presentation quality and accuracy.`,
        metadata: {
          description: `Meta description from ${url}`,
          keywords: ['content', 'information', 'data'],
          author: 'Content Author',
          publishDate: new Date().toISOString()
        },
        extractedAt: new Date().toISOString()
      };

      this.setCache(cacheKey, mockContent, 10 * 60 * 1000); // 10 minutes TTL

      return {
        success: true,
        data: mockContent,
        source: 'content-fetch'
      };
    } catch (error) {
      console.error('❌ Content fetch error:', error);
      return {
        success: false,
        error: 'Failed to fetch web content',
        source: 'content-fetch'
      };
    }
  }

  /**
   * Get service status and capabilities
   */
  getServiceStatus(): { [key: string]: MCPService } {
    const status: { [key: string]: MCPService } = {};
    this.services.forEach((service, name) => {
      status[name] = { ...service };
    });
    return status;
  }

  /**
   * Cache management
   */
  private getFromCache(key: string): any | null {
    const cached = this.cache.get(key);
    if (cached && Date.now() - cached.timestamp < cached.ttl) {
      return cached.data;
    }
    if (cached) {
      this.cache.delete(key);
    }
    return null;
  }

  private setCache(key: string, data: any, ttl: number = this.CACHE_TTL): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    });
  }

  /**
   * Clear cache
   */
  clearCache(): void {
    this.cache.clear();
    console.log('🧹 MCP Service cache cleared');
  }
}

// Export singleton instance
export const mcpServiceManager = new MCPServiceManager();
export type { MCPResponse, SearchResult, ChartConfig };
