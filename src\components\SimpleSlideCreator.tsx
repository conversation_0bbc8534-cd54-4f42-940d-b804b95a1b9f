'use client'

import React, { useState } from 'react'
import { motion } from 'framer-motion'

interface SimpleSlideCreatorProps {
  onCreateSlides: (topic: string) => void
  isVisible: boolean
  locale?: 'ar' | 'en'
}

const SimpleSlideCreator: React.FC<SimpleSlideCreatorProps> = ({
  onCreateSlides,
  isVisible,
  locale = 'ar'
}) => {
  const [topic, setTopic] = useState('')
  const [isCreating, setIsCreating] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!topic.trim()) return

    setIsCreating(true)
    await onCreateSlides(topic)
    setIsCreating(false)
    setTopic('')
  }

  const suggestedTopics = locale === 'ar' ? [
    '🦁 الحيوانات',
    '🌍 الكواكب',
    '🌈 الألوان',
    '🔢 الأرقام',
    '🍎 الفواكه',
    '⚽ الرياضة'
  ] : [
    '🦁 Animals',
    '🌍 Planets',
    '🌈 Colors',
    '🔢 Numbers',
    '🍎 Fruits',
    '⚽ Sports'
  ]

  if (!isVisible) return null

  return (
    <div className="max-w-2xl mx-auto p-6 bg-white dark:bg-gray-800 rounded-3xl shadow-lg border-4 border-blue-200 dark:border-blue-700">
      {/* Fun Header */}
      <div className="text-center mb-8">
        <motion.div
          animate={{ rotate: [0, 10, -10, 0] }}
          transition={{ duration: 2, repeat: Infinity }}
          className="text-6xl mb-4"
        >
          🎨
        </motion.div>
        <h2 className="text-3xl font-bold text-blue-600 dark:text-blue-400 mb-2">
          {locale === 'ar' ? 'منشئ العروض السحري!' : 'Magic Slide Creator!'}
        </h2>
        <p className="text-lg text-gray-600 dark:text-gray-400">
          {locale === 'ar' 
            ? 'أخبرني عن أي موضوع وسأصنع لك عرضاً رائعاً!' 
            : 'Tell me any topic and I\'ll make you an amazing presentation!'
          }
        </p>
      </div>

      {/* Simple Form */}
      <form onSubmit={handleSubmit} className="space-y-6">
        <div>
          <label className="block text-xl font-semibold text-gray-700 dark:text-gray-300 mb-3">
            {locale === 'ar' ? '🤔 عن ماذا تريد عرضاً؟' : '🤔 What do you want a presentation about?'}
          </label>
          <input
            type="text"
            value={topic}
            onChange={(e) => setTopic(e.target.value)}
            placeholder={locale === 'ar' ? 'مثال: الديناصورات' : 'Example: Dinosaurs'}
            className="w-full p-4 text-lg border-3 border-blue-300 dark:border-blue-600 rounded-2xl focus:border-blue-500 focus:outline-none bg-blue-50 dark:bg-blue-900/20 text-gray-800 dark:text-gray-200"
            disabled={isCreating}
          />
        </div>

        {/* Suggested Topics */}
        <div>
          <p className="text-lg font-semibold text-gray-700 dark:text-gray-300 mb-3">
            {locale === 'ar' ? '💡 أو اختر من هذه المواضيع:' : '💡 Or choose from these topics:'}
          </p>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
            {suggestedTopics.map((suggestedTopic, index) => (
              <motion.button
                key={index}
                type="button"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => setTopic(suggestedTopic.split(' ').slice(1).join(' '))}
                className="p-3 bg-gradient-to-r from-purple-400 to-pink-400 text-white rounded-xl font-semibold text-sm hover:from-purple-500 hover:to-pink-500 transition-all duration-200 disabled:opacity-50"
                disabled={isCreating}
              >
                {suggestedTopic}
              </motion.button>
            ))}
          </div>
        </div>

        {/* Create Button */}
        <motion.button
          type="submit"
          disabled={!topic.trim() || isCreating}
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
          className="w-full p-4 bg-gradient-to-r from-green-400 to-blue-500 text-white text-xl font-bold rounded-2xl hover:from-green-500 hover:to-blue-600 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2 rtl:space-x-reverse"
        >
          {isCreating ? (
            <>
              <motion.div
                animate={{ rotate: 360 }}
                transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                className="w-6 h-6 border-3 border-white border-t-transparent rounded-full"
              />
              <span>{locale === 'ar' ? 'جاري الإنشاء...' : 'Creating...'}</span>
            </>
          ) : (
            <>
              <span className="text-2xl">✨</span>
              <span>{locale === 'ar' ? 'أنشئ عرضي السحري!' : 'Create My Magic Slides!'}</span>
            </>
          )}
        </motion.button>
      </form>

      {/* Fun Facts */}
      <div className="mt-8 p-4 bg-yellow-100 dark:bg-yellow-900/20 rounded-2xl border-2 border-yellow-300 dark:border-yellow-700">
        <div className="flex items-center space-x-2 rtl:space-x-reverse mb-2">
          <span className="text-2xl">🌟</span>
          <h3 className="text-lg font-bold text-yellow-800 dark:text-yellow-300">
            {locale === 'ar' ? 'هل تعلم؟' : 'Did you know?'}
          </h3>
        </div>
        <p className="text-yellow-700 dark:text-yellow-400">
          {locale === 'ar' 
            ? 'يمكنني إنشاء عروض تقديمية عن أي موضوع تريده! من الحيوانات إلى الفضاء، ومن الرياضة إلى العلوم!'
            : 'I can create presentations about any topic you want! From animals to space, from sports to science!'
          }
        </p>
      </div>

      {/* Tips */}
      <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="p-3 bg-blue-100 dark:bg-blue-900/20 rounded-xl border border-blue-300 dark:border-blue-700">
          <div className="flex items-center space-x-2 rtl:space-x-reverse mb-1">
            <span className="text-lg">💡</span>
            <h4 className="font-semibold text-blue-800 dark:text-blue-300">
              {locale === 'ar' ? 'نصيحة' : 'Tip'}
            </h4>
          </div>
          <p className="text-sm text-blue-700 dark:text-blue-400">
            {locale === 'ar' 
              ? 'كن محدداً! بدلاً من "حيوانات"، جرب "الأسود الأفريقية"'
              : 'Be specific! Instead of "animals", try "African lions"'
            }
          </p>
        </div>

        <div className="p-3 bg-green-100 dark:bg-green-900/20 rounded-xl border border-green-300 dark:border-green-700">
          <div className="flex items-center space-x-2 rtl:space-x-reverse mb-1">
            <span className="text-lg">🚀</span>
            <h4 className="font-semibold text-green-800 dark:text-green-300">
              {locale === 'ar' ? 'سريع' : 'Fast'}
            </h4>
          </div>
          <p className="text-sm text-green-700 dark:text-green-400">
            {locale === 'ar' 
              ? 'سأنشئ عرضك في أقل من دقيقة!'
              : 'I\'ll create your presentation in less than a minute!'
            }
          </p>
        </div>
      </div>
    </div>
  )
}

export default SimpleSlideCreator
