'use client'

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Menu, 
  Plus, 
  Sun, 
  Moon, 
  Globe,
  MessageSquare,
  Presentation,
  Code,
  FileText,
  PenTool,
  Search,
  ChevronDown
} from 'lucide-react'
import { t } from '@/locales'
import type { HeaderProps, ChatMode } from '@/types'

const chatModes: { id: ChatMode; icon: any }[] = [
  { id: 'chat', icon: MessageSquare },
  { id: 'slides', icon: Presentation },
  { id: 'code', icon: Code },
  { id: 'summarizer', icon: FileText },
  { id: 'writer', icon: PenTool },
  { id: 'research', icon: Search },
]

export function Header({
  onToggleSidebar,
  onToggleTheme,
  onToggleLocale,
  onNewChat,
  currentMode,
  onModeChange,
  locale,
}: HeaderProps) {
  const [modeDropdownOpen, setModeDropdownOpen] = useState(false)

  const currentModeData = chatModes.find(mode => mode.id === currentMode)
  const CurrentModeIcon = currentModeData?.icon || MessageSquare

  return (
    <header className="sticky top-0 z-50 bg-light-secondary dark:bg-dark-secondary border-b border-light-border dark:border-dark-border">
      <div className="flex items-center justify-between h-14 sm:h-16 px-2 sm:px-4 mobile-header-compact">
        {/* Left Section */}
        <div className="flex items-center space-x-2 sm:space-x-4">
          {/* Sidebar Toggle */}
          <button
            onClick={onToggleSidebar}
            className="p-1.5 sm:p-2 rounded-lg hover:bg-light-tertiary dark:hover:bg-dark-tertiary transition-colors touch-target"
            aria-label={t(locale, 'a11y.toggleSidebar')}
          >
            <Menu className="w-4 h-4 sm:w-5 sm:h-5 text-light-text-secondary dark:text-dark-text-secondary" />
          </button>

          {/* Logo */}
          <div className="flex items-center space-x-2 sm:space-x-3">
            <div className="w-6 h-6 sm:w-8 sm:h-8 relative">
              <svg
                viewBox="0 0 100 100"
                className="w-full h-full text-light-accent dark:text-dark-accent"
                fill="currentColor"
                xmlns="http://www.w3.org/2000/svg"
              >
                <circle cx="35" cy="40" r="3" />
                <circle cx="65" cy="40" r="3" />
                <path
                  d="M30 60 Q50 75 70 60"
                  stroke="currentColor"
                  strokeWidth="2"
                  fill="none"
                />
                <text
                  x="50"
                  y="25"
                  textAnchor="middle"
                  fontSize="10"
                  fontWeight="bold"
                  className="fill-current"
                >
                  WIDDX
                </text>
              </svg>
            </div>
            <h1 className="text-lg sm:text-xl font-bold text-light-text-primary dark:text-dark-text-primary">
              WIDDX AI
            </h1>
          </div>
        </div>

        {/* Center Section - Mode Selector */}
        <div className="hidden md:flex items-center space-x-2">
          <div className="relative">
            <button
              onClick={() => setModeDropdownOpen(!modeDropdownOpen)}
              className="flex items-center space-x-2 px-4 py-2 rounded-lg bg-light-tertiary dark:bg-dark-tertiary hover:bg-light-border dark:hover:bg-dark-border transition-colors"
            >
              <CurrentModeIcon className="w-4 h-4 text-light-accent dark:text-dark-accent" />
              <span className="text-sm font-medium text-light-text-primary dark:text-dark-text-primary">
                {t(locale, `modes.${currentMode}.name`)}
              </span>
              <ChevronDown className="w-4 h-4 text-light-text-secondary dark:text-dark-text-secondary" />
            </button>

            <AnimatePresence>
              {modeDropdownOpen && (
                <motion.div
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  transition={{ duration: 0.2 }}
                  className="absolute top-full left-0 mt-2 w-64 bg-light-secondary dark:bg-dark-secondary border border-light-border dark:border-dark-border rounded-lg shadow-lg z-50"
                >
                  <div className="p-2">
                    {chatModes.map((mode) => {
                      const Icon = mode.icon
                      return (
                        <button
                          key={mode.id}
                          onClick={() => {
                            onModeChange(mode.id)
                            setModeDropdownOpen(false)
                          }}
                          className={`w-full flex items-start space-x-3 p-3 rounded-lg transition-colors ${
                            currentMode === mode.id
                              ? 'bg-light-accent dark:bg-dark-accent text-white'
                              : 'hover:bg-light-tertiary dark:hover:bg-dark-tertiary text-light-text-primary dark:text-dark-text-primary'
                          }`}
                        >
                          <Icon className="w-5 h-5 mt-0.5 flex-shrink-0" />
                          <div className="text-left">
                            <div className="font-medium text-sm">
                              {t(locale, `modes.${mode.id}.name`)}
                            </div>
                            <div className={`text-xs mt-0.5 ${
                              currentMode === mode.id
                                ? 'text-white/80'
                                : 'text-light-text-secondary dark:text-dark-text-secondary'
                            }`}>
                              {t(locale, `modes.${mode.id}.description`)}
                            </div>
                          </div>
                        </button>
                      )
                    })}
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </div>

        {/* Right Section */}
        <div className="flex items-center space-x-1 sm:space-x-2">
          {/* Language Toggle */}
          <button
            onClick={onToggleLocale}
            className="p-1.5 sm:p-2 rounded-lg hover:bg-light-tertiary dark:hover:bg-dark-tertiary transition-colors touch-target"
            aria-label={t(locale, 'a11y.toggleLanguage')}
          >
            <Globe className="w-4 h-4 sm:w-5 sm:h-5 text-light-text-secondary dark:text-dark-text-secondary" />
          </button>

          {/* Theme Toggle */}
          <button
            onClick={onToggleTheme}
            className="p-1.5 sm:p-2 rounded-lg hover:bg-light-tertiary dark:hover:bg-dark-tertiary transition-colors touch-target"
            aria-label={t(locale, 'a11y.toggleTheme')}
          >
            <Sun className="w-4 h-4 sm:w-5 sm:h-5 text-light-text-secondary dark:text-dark-text-secondary block dark:hidden" />
            <Moon className="w-4 h-4 sm:w-5 sm:h-5 text-light-text-secondary dark:text-dark-text-secondary hidden dark:block" />
          </button>

          {/* New Chat Button */}
          <button
            onClick={onNewChat}
            className="flex items-center space-x-1 sm:space-x-2 px-2 sm:px-4 py-1.5 sm:py-2 bg-light-accent dark:bg-dark-accent hover:bg-light-accent-hover dark:hover:bg-dark-accent-hover text-white rounded-lg transition-colors touch-target mobile-btn-sm"
            aria-label={t(locale, 'a11y.newChat')}
          >
            <Plus className="w-4 h-4" />
            <span className="hidden sm:inline text-sm font-medium">
              {t(locale, 'nav.newChat')}
            </span>
          </button>
        </div>
      </div>

      {/* Mobile Mode Selector */}
      <div className="md:hidden border-t border-light-border dark:border-dark-border">
        <div className="flex overflow-x-auto scrollbar-hide">
          {chatModes.map((mode) => {
            const Icon = mode.icon
            return (
              <button
                key={mode.id}
                onClick={() => onModeChange(mode.id)}
                className={`flex-shrink-0 flex items-center space-x-2 px-4 py-3 transition-colors ${
                  currentMode === mode.id
                    ? 'bg-light-accent dark:bg-dark-accent text-white border-b-2 border-light-accent dark:border-dark-accent'
                    : 'text-light-text-secondary dark:text-dark-text-secondary hover:text-light-text-primary dark:hover:text-dark-text-primary'
                }`}
              >
                <Icon className="w-4 h-4" />
                <span className="text-sm font-medium whitespace-nowrap">
                  {t(locale, `modes.${mode.id}.name`)}
                </span>
              </button>
            )
          })}
        </div>
      </div>

      {/* Click outside to close dropdown */}
      {modeDropdownOpen && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setModeDropdownOpen(false)}
        />
      )}
    </header>
  )
}