'use client'

import { motion, AnimatePresence } from 'framer-motion'
import { Bo<PERSON> } from 'lucide-react'
import type { TypingIndicatorProps } from '@/types'

export function TypingIndicator({ isVisible, className = '' }: TypingIndicatorProps) {
  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -10 }}
          transition={{ duration: 0.3 }}
          className={`flex gap-4 ${className}`}
        >
          {/* Avatar */}
          <div className="flex-shrink-0">
            <div className="w-10 h-10 rounded-full bg-light-secondary dark:bg-dark-secondary border border-light-border dark:border-dark-border flex items-center justify-center text-light-text-primary dark:text-dark-text-primary">
              <Bot className="w-5 h-5" />
            </div>
          </div>

          {/* Typing Animation */}
          <div className="flex-1">
            <div className="bg-light-secondary dark:bg-dark-secondary border border-light-border dark:border-dark-border rounded-2xl rounded-tl-md px-4 py-3">
              <div className="flex items-center space-x-1">
                <span className="text-sm text-light-text-secondary dark:text-dark-text-secondary mr-2">
                  Widdx.aiis typing
                </span>
                <div className="flex space-x-1">
                  {[0, 1, 2].map((i) => (
                    <motion.div
                      key={i}
                      className="w-2 h-2 bg-light-text-muted dark:text-dark-text-muted rounded-full"
                      animate={{
                        scale: [1, 1.2, 1],
                        opacity: [0.5, 1, 0.5],
                      }}
                      transition={{
                        duration: 1.4,
                        repeat: Infinity,
                        delay: i * 0.16,
                        ease: 'easeInOut',
                      }}
                    />
                  ))}
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  )
}