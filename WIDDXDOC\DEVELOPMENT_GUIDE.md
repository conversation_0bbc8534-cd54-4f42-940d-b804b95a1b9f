# Next.js Chat Application - Development Guide

## Prerequisites
- Node.js v18+
- MongoDB (local or connection string)
- API keys for:
  - DeepSeek AI
  - Gemini (optional fallback)

## Installation
1. Clone repository:
```bash
git clone https://github.com/your-repo/next-chat-app.git
cd next-chat-app
```

2. Install dependencies:
```bash
npm install
```

3. Configure environment:
```bash
cp .env.example .env.local
```

4. Update `.env.local` with:
- MongoDB connection string
- AI service API keys
- Any other required configurations

## Development Workflow

### Running Locally
```bash
npm run dev
```

### Key Scripts
- `dev`: Start development server
- `build`: Create production build
- `start`: Run production build
- `lint`: Run ESLint checks
- `format`: Format code with Prettier

## Testing
```bash
npm test
```

## Deployment

### Vercel
1. Connect your GitHub repository
2. Set environment variables
3. Deploy!

### Docker
```bash
docker build -t next-chat-app .
docker run -p 3000:3000 next-chat-app
```

## Contribution Guidelines
1. Create feature branch from `main`
2. Follow code style guidelines
3. Write tests for new features
4. Submit PR with clear description