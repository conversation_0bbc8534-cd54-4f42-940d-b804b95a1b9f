# System Architecture Diagram

```mermaid
graph TD
    subgraph Frontend
        A[Next.js App] --> B[Chat Interface]
        A --> C[Slide Panel]
        A --> D[Localization]
    end

    subgraph Backend
        E[API Routes] --> F[AI Service Layer]
        F --> G[DeepSeek]
        F --> H[Gemini Fallback]
        E --> I[Database]
    end

    B -->|User Input| E
    E -->|AI Response| B
    E -->|Slide Data| C
    G -->|API Calls| J[External AI API]
    H -->|API Calls| K[External AI API]
    I[(MongoDB)]
```

## Component Descriptions

1. **Frontend**:
   - Next.js application with React components
   - Handles all UI rendering and user interactions

2. **Backend**:
   - Next.js API routes for business logic
   - AI service abstraction layer with fallback
   - Database operations via Mongoose

3. **Data Flow**:
   - User input → Chat Interface → API Routes
   - API Routes → AI Services → Response Processing
   - Processed response → Slide Generation → UI Rendering