'use client'

import { useState, useEffect } from 'react'
import type { Locale } from '@/types'

export function useLocale() {
  const [locale, setLocale] = useState<Locale>('en')

  useEffect(() => {
    // Get saved locale from localStorage
    const savedLocale = localStorage.getItem('widdx-ai-locale') as Locale
    if (savedLocale && (savedLocale === 'en' || savedLocale === 'ar')) {
      setLocale(savedLocale)
    } else {
      // Detect browser language
      const browserLang = navigator.language.toLowerCase()
      if (browserLang.startsWith('ar')) {
        setLocale('ar')
      }
    }
  }, [])

  useEffect(() => {
    // Update document direction and language
    document.documentElement.lang = locale
    document.documentElement.dir = locale === 'ar' ? 'rtl' : 'ltr'
    
    // Save to localStorage
    localStorage.setItem('widdx-ai-locale', locale)
  }, [locale])

  return { locale, setLocale }
}