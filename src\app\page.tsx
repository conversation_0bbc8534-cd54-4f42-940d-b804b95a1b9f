'use client'

import { useState, useEffect } from 'react'
import { ChatInterface } from '@/components/ChatInterface'
import { SplashScreen } from '@/components/SplashScreen'
import { useLocale } from '@/hooks/useLocale'

export default function Home() {
  const [isLoading, setIsLoading] = useState(true)
  const { locale, setLocale } = useLocale()

  useEffect(() => {
    // Simulate loading time like Z.ai
    const timer = setTimeout(() => {
      setIsLoading(false)
    }, 2500)

    return () => clearTimeout(timer)
  }, [])

  if (isLoading) {
    return <SplashScreen />
  }

  return (
    <main className="h-screen overflow-hidden">
      <ChatInterface locale={locale} onLocaleChange={setLocale} />
    </main>
  )
}