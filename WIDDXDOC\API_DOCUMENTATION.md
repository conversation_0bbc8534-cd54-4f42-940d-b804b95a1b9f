# API Documentation

## Chat Endpoints

### POST /api/chat
- **Description**: Main chat endpoint for AI conversations
- **Parameters**:
  - `messages`: Array of chat messages (required)
  - `mode`: Chat mode (default: 'chat')
  - `locale`: Language locale (default: 'en')
  - `userId`: Optional user identifier
  - `stream`: Whether to stream response (default: false)
- **Responses**:
  - Streaming: Returns text/plain chunks with model metadata
  - Non-streaming: Returns JSON response with AI output

### POST /api/chat/stream
- **Description**: Dedicated streaming endpoint with Server-Sent Events (SSE)
- **Parameters**: Same as /api/chat (except stream is always true)
- **Response**: SSE stream with metadata, content chunks, and completion signal

## System Endpoints

### GET /api/health
- **Description**: System health check
- **Response**:
  - Status of AI services (DeepSeek, Gemini)
  - Environment configuration
  - Overall system health status

### POST /api/test-fallback
- **Description**: Fallback endpoint for slide generation
- **Parameters**:
  - `topic`: Presentation topic (default: 'الذكاء الاصطناعي والتعلم الآلي')
  - `template`: Template name (default: 'arabic-professional')
- **Response**: Generated presentation content in JSON format