export type Locale = 'en' | 'ar'

export interface Message {
  id: string
  content: string
  role: 'user' | 'assistant'
  timestamp: Date
  isTyping?: boolean
  metadata?: {
    model?: string
    tokens?: number
    processingTime?: number
    slideData?: {
      html: string
      title: string
    }
  }
}

export interface Chat {
  id: string
  title: string
  messages: Message[]
  createdAt: Date
  updatedAt: Date
  mode: ChatMode
}

export type ChatMode = 'chat' | 'slides' | 'code' | 'summarizer' | 'writer' | 'research'

export interface ChatModeConfig {
  id: ChatMode
  name: string
  nameAr: string
  description: string
  descriptionAr: string
  icon: string
  placeholder: string
  placeholderAr: string
  systemPrompt: string
  features: string[]
  featuresAr: string[]
}

export interface User {
  id: string
  name?: string
  email?: string
  avatar?: string
  preferences: {
    theme: 'light' | 'dark' | 'system'
    locale: Locale
    defaultMode: ChatMode
  }
}

export interface AppState {
  currentChat: Chat | null
  chats: Chat[]
  user: User | null
  isLoading: boolean
  error: string | null
  sidebarOpen: boolean
  currentMode: ChatMode
}

export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

export interface StreamResponse {
  content: string
  done: boolean
  metadata?: {
    model: string
    tokens: number
    processingTime: number
  }
}

export interface ModelConfig {
  id: string
  name: string
  description: string
  maxTokens: number
  temperature: number
  topP: number
  frequencyPenalty: number
  presencePenalty: number
}

export interface TypingIndicatorProps {
  isVisible: boolean
  className?: string
}

export interface MessageProps {
  message: Message
  isLast?: boolean
  onRegenerate?: () => void
  onCopy?: () => void
  onEdit?: (content: string) => void
}

export interface ChatInputProps {
  onSendMessage: (content: string) => void
  disabled?: boolean
  placeholder?: string
  maxLength?: number
  currentMode: ChatMode
  locale: Locale
}

export interface SidebarProps {
  isOpen: boolean
  onToggle: () => void
  chats: Chat[]
  currentChat: Chat | null
  onSelectChat: (chat: Chat) => void
  onNewChat: () => void
  onDeleteChat: (chatId: string) => void
  locale: Locale
}

export interface HeaderProps {
  onToggleSidebar: () => void
  onToggleTheme: () => void
  onToggleLocale: () => void
  onNewChat: () => void
  currentMode: ChatMode
  onModeChange: (mode: ChatMode) => void
  locale: Locale
}

export interface WelcomeScreenProps {
  onModeSelect: (mode: ChatMode) => void
  onPromptSelect: (prompt: string) => void
  currentMode: ChatMode
  locale: Locale
}

export interface ErrorBoundaryState {
  hasError: boolean
  error?: Error
}

export interface LoadingState {
  isLoading: boolean
  message?: string
  progress?: number
}

// Animation types
export interface AnimationConfig {
  duration: number
  easing: string
  delay?: number
}

export interface TransitionConfig {
  enter: AnimationConfig
  exit: AnimationConfig
}

// Theme types
export interface ThemeColors {
  primary: string
  secondary: string
  tertiary: string
  accent: string
  accentHover: string
  text: {
    primary: string
    secondary: string
    muted: string
  }
  border: string
  success: string
  warning: string
  error: string
}

export interface Theme {
  name: string
  colors: ThemeColors
}

// Utility types
export type DeepPartial<T> = {
  [P in keyof T]?: DeepPartial<T[P]>
}

export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>

export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>

// Event types
export interface ChatEvent {
  type: 'message_sent' | 'message_received' | 'typing_start' | 'typing_stop' | 'error'
  payload: any
  timestamp: Date
}

export interface KeyboardShortcut {
  key: string
  ctrlKey?: boolean
  shiftKey?: boolean
  altKey?: boolean
  metaKey?: boolean
  action: () => void
  description: string
  descriptionAr: string
}