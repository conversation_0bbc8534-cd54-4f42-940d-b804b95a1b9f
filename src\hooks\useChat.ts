'use client'

import { useState, useCallback, useRef, useEffect } from 'react'
import { v4 as uuidv4 } from 'uuid'
import { apiClient } from '@/lib/api-client'
import type { Chat, Message, ChatMode, Locale } from '@/types'

export function useChat(locale: Locale) {
  const [chats, setChats] = useState<Chat[]>([])
  const [currentChat, setCurrentChat] = useState<Chat | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [isTyping, setIsTyping] = useState(false)
  const abortControllerRef = useRef<AbortController | null>(null)

  // Load chats from localStorage on mount
  useEffect(() => {
    const savedChats = localStorage.getItem('widdx-ai-chats')
    if (savedChats) {
      try {
        const parsedChats = JSON.parse(savedChats).map((chat: any) => ({
          ...chat,
          createdAt: new Date(chat.createdAt),
          updatedAt: new Date(chat.updatedAt),
          messages: chat.messages.map((msg: any) => ({
            ...msg,
            timestamp: new Date(msg.timestamp),
          })),
        }))
        setChats(parsedChats)
      } catch (error) {
        console.error('Error loading chats:', error)
      }
    }
  }, [])

  // Save chats to localStorage whenever chats change
  useEffect(() => {
    if (chats.length > 0) {
      localStorage.setItem('widdx-ai-chats', JSON.stringify(chats))
    }
  }, [chats])

  const createNewChat = useCallback((mode: ChatMode = 'chat') => {
    const newChat: Chat = {
      id: uuidv4(),
      title: locale === 'ar' ? 'محادثة جديدة' : 'New Chat',
      messages: [],
      createdAt: new Date(),
      updatedAt: new Date(),
      mode,
    }
    
    setCurrentChat(newChat)
    setChats(prev => [newChat, ...prev])
    return newChat
  }, [locale])

  const selectChat = useCallback((chat: Chat) => {
    setCurrentChat(chat)
  }, [])

  const deleteChat = useCallback((chatId: string) => {
    setChats(prev => prev.filter(chat => chat.id !== chatId))
    if (currentChat?.id === chatId) {
      setCurrentChat(null)
    }
  }, [currentChat])

  const updateChatTitle = useCallback((chatId: string, title: string) => {
    setChats(prev => prev.map(chat => 
      chat.id === chatId 
        ? { ...chat, title, updatedAt: new Date() }
        : chat
    ))
    
    if (currentChat?.id === chatId) {
      setCurrentChat(prev => prev ? { ...prev, title } : null)
    }
  }, [currentChat])

  const addMessage = useCallback((message: Omit<Message, 'id' | 'timestamp'>) => {
    const newMessage: Message = {
      ...message,
      id: uuidv4(),
      timestamp: new Date(),
    }

    if (!currentChat) {
      const newChat = createNewChat()
      const updatedChat = {
        ...newChat,
        messages: [newMessage],
        title: generateChatTitle(newMessage.content, locale),
        updatedAt: new Date(),
      }

      setCurrentChat(updatedChat)
      setChats(prev => [updatedChat, ...prev.slice(1)])
    } else {
      const updatedChat = {
        ...currentChat,
        messages: [...currentChat.messages, newMessage],
        updatedAt: new Date(),
      }

      // Update title if this is the first user message
      if (currentChat.messages.length === 0 && message.role === 'user') {
        updatedChat.title = generateChatTitle(message.content, locale)
      }

      setCurrentChat(updatedChat)
      setChats(prev => prev.map(chat =>
        chat.id === currentChat.id ? updatedChat : chat
      ))
    }

    return newMessage
  }, [currentChat, createNewChat, locale])
 
  const sendMessage = useCallback(async (content: string) => {
    if (!content.trim() || isLoading) return

    // Cancel any ongoing request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
    }

    setIsLoading(true)
    setIsTyping(false)

    try {
      // Create user message
      const userMessage: Message = {
        id: uuidv4(),
        content: content.trim(),
        role: 'user',
        timestamp: new Date()
      }

      // Create or update chat with user message
      let chatToUpdate: Chat
      if (!currentChat) {
        chatToUpdate = {
          ...createNewChat(),
          messages: [userMessage],
          title: generateChatTitle(userMessage.content, locale),
          updatedAt: new Date(),
        }
        setCurrentChat(chatToUpdate)
        setChats(prev => [chatToUpdate, ...prev.slice(1)])
      } else {
        chatToUpdate = {
          ...currentChat,
          messages: [...currentChat.messages, userMessage],
          updatedAt: new Date(),
        }
        // Update title if this is the first user message
        if (currentChat.messages.length === 0) {
          chatToUpdate.title = generateChatTitle(userMessage.content, locale)
        }
        setCurrentChat(chatToUpdate)
        setChats(prev => prev.map(chat =>
          chat.id === currentChat.id ? chatToUpdate : chat
        ))
      }

      // Show typing indicator
      setIsTyping(true)

      // Prepare messages for API
      const messages = chatToUpdate.messages

      // Generate AI response using API client
      const result = await apiClient.sendMessage({
        messages,
        mode: chatToUpdate.mode || 'chat',
        locale,
        userId: 'user'
      })

      setIsTyping(false)

      // Create assistant message with special handling for slides
      let assistantContent = result.content
      let slideData = null

      // Check if this is a slide presentation
      if (chatToUpdate.mode === 'slides' && result.content.includes('<!DOCTYPE html>')) {
        slideData = {
          html: result.content,
          title: `عرض تقديمي عن: ${chatToUpdate.messages[chatToUpdate.messages.length - 1]?.content || 'موضوع'}`
        }

        // Create a summary message instead of the full HTML
        assistantContent = locale === 'ar'
          ? `تم إنشاء عرض تقديمي احترافي بنجاح! 🎉\n\nالعرض يحتوي على:\n• شرائح متقدمة ومصممة بعناية\n• محتوى عالي الجودة ومفصل\n• تصميم احترافي مع رسوم متحركة\n• تنقل سهل وتفاعلي\n\nانقر على زر "عرض العرض التقديمي" لمشاهدة العرض كاملاً.`
          : `Professional presentation created successfully! 🎉\n\nThe presentation includes:\n• Advanced and carefully designed slides\n• High-quality and detailed content\n• Professional design with animations\n• Easy and interactive navigation\n\nClick the "Show Presentation" button to view the full presentation.`
      }

      const assistantMessage: Message = {
        id: uuidv4(),
        content: assistantContent,
        role: 'assistant',
        timestamp: new Date(),
        metadata: {
          model: result.model,
          tokens: result.tokens,
          processingTime: result.processingTime,
          slideData: slideData || undefined
        },
      }

      // Add assistant message to the same chat
      const finalChat = {
        ...chatToUpdate,
        messages: [...chatToUpdate.messages, assistantMessage],
        updatedAt: new Date(),
      }

      setCurrentChat(finalChat)
      setChats(prev => prev.map(chat =>
        chat.id === chatToUpdate.id ? finalChat : chat
      ))

    } catch (error) {
      setIsTyping(false)
      console.error('Error sending message:', error)
      addMessage({
        content: locale === 'ar'
          ? 'عذراً، حدث خطأ أثناء معالجة رسالتك. يرجى المحاولة مرة أخرى.'
          : 'Sorry, there was an error processing your message. Please try again.',
        role: 'assistant',
      })
    } finally {
      setIsLoading(false)
      abortControllerRef.current = null
    }
  }, [addMessage, currentChat, isLoading, locale, createNewChat])

  const regenerateLastResponse = useCallback(async () => {
    if (!currentChat || currentChat.messages.length < 2) return

    const lastUserMessage = [...currentChat.messages]
      .reverse()
      .find(msg => msg.role === 'user')

    if (!lastUserMessage) return

    // Remove last assistant message
    const updatedMessages = currentChat.messages.slice(0, -1)
    const updatedChat = {
      ...currentChat,
      messages: updatedMessages,
      updatedAt: new Date(),
    }
    
    setCurrentChat(updatedChat)
    setChats(prev => prev.map(chat => 
      chat.id === currentChat.id ? updatedChat : chat
    ))

    // Regenerate response
    await sendMessage(lastUserMessage.content)
  }, [currentChat, sendMessage])

  const clearChat = useCallback(() => {
    if (!currentChat) return

    const clearedChat = {
      ...currentChat,
      messages: [],
      updatedAt: new Date(),
    }
    
    setCurrentChat(clearedChat)
    setChats(prev => prev.map(chat => 
      chat.id === currentChat.id ? clearedChat : chat
    ))
  }, [currentChat])

  return {
    chats,
    currentChat,
    isLoading,
    isTyping,
    createNewChat,
    selectChat,
    deleteChat,
    updateChatTitle,
    sendMessage,
    regenerateLastResponse,
    clearChat,
  }
}

function generateChatTitle(content: string, locale: Locale): string {
  const maxLength = 30
  const truncated = content.length > maxLength 
    ? content.substring(0, maxLength) + '...'
    : content
  
  return truncated || (locale === 'ar' ? 'محادثة جديدة' : 'New Chat')
}
