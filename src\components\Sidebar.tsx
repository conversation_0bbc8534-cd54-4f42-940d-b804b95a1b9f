'use client'

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  X, 
  Plus, 
  Trash2, 
  MessageSquare,
  Calendar,
  Clock,
  MoreHorizontal
} from 'lucide-react'
import { t } from '@/locales'
import type { SidebarProps, Chat } from '@/types'

export function Sidebar({
  isOpen,
  onToggle,
  chats,
  currentChat,
  onSelectChat,
  onNewChat,
  onDeleteChat,
  locale,
}: SidebarProps) {
  const [deleteConfirm, setDeleteConfirm] = useState<string | null>(null)

  const groupChatsByDate = (chats: Chat[]) => {
    const now = new Date()
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
    const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000)
    const lastWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000)
    const lastMonth = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000)

    const groups = {
      today: [] as Chat[],
      yesterday: [] as Chat[],
      lastWeek: [] as Chat[],
      lastMonth: [] as Chat[],
      older: [] as Chat[],
    }

    chats.forEach(chat => {
      const chatDate = new Date(chat.updatedAt)
      if (chatDate >= today) {
        groups.today.push(chat)
      } else if (chatDate >= yesterday) {
        groups.yesterday.push(chat)
      } else if (chatDate >= lastWeek) {
        groups.lastWeek.push(chat)
      } else if (chatDate >= lastMonth) {
        groups.lastMonth.push(chat)
      } else {
        groups.older.push(chat)
      }
    })

    return groups
  }

  const formatTime = (date: Date) => {
    const now = new Date()
    const diff = now.getTime() - date.getTime()
    const minutes = Math.floor(diff / (1000 * 60))
    const hours = Math.floor(diff / (1000 * 60 * 60))
    const days = Math.floor(diff / (1000 * 60 * 60 * 24))

    if (minutes < 1) return t(locale, 'time.now')
    if (minutes < 60) return t(locale, 'time.minutesAgo', { count: minutes.toString() })
    if (hours < 24) return t(locale, 'time.hoursAgo', { count: hours.toString() })
    if (days < 7) return t(locale, 'time.daysAgo', { count: days.toString() })
    
    return date.toLocaleDateString(locale === 'ar' ? 'ar-SA' : 'en-US', {
      month: 'short',
      day: 'numeric',
    })
  }

  const handleDeleteChat = (chatId: string, e: React.MouseEvent) => {
    e.stopPropagation()
    if (deleteConfirm === chatId) {
      onDeleteChat(chatId)
      setDeleteConfirm(null)
    } else {
      setDeleteConfirm(chatId)
      setTimeout(() => setDeleteConfirm(null), 3000)
    }
  }

  const groupedChats = groupChatsByDate(chats)

  return (
    <>
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ x: -300 }}
            animate={{ x: 0 }}
            exit={{ x: -300 }}
            transition={{ type: 'spring', damping: 25, stiffness: 200 }}
            className="fixed lg:relative top-0 left-0 h-full w-full sm:w-80 lg:w-80 bg-light-secondary dark:bg-dark-secondary border-r border-light-border dark:border-dark-border z-50 lg:z-auto flex flex-col"
          >
            {/* Header */}
            <div className="flex items-center justify-between p-4 border-b border-light-border dark:border-dark-border">
              <h2 className="text-lg font-semibold text-light-text-primary dark:text-dark-text-primary">
                {t(locale, 'sidebar.title')}
              </h2>
              <div className="flex items-center space-x-2">
                <button
                  onClick={onNewChat}
                  className="p-2 rounded-lg hover:bg-light-tertiary dark:hover:bg-dark-tertiary transition-colors"
                  aria-label={t(locale, 'a11y.newChat')}
                >
                  <Plus className="w-4 h-4 text-light-text-secondary dark:text-dark-text-secondary" />
                </button>
                <button
                  onClick={onToggle}
                  className="p-2 rounded-lg hover:bg-light-tertiary dark:hover:bg-dark-tertiary transition-colors lg:hidden"
                  aria-label={t(locale, 'a11y.toggleSidebar')}
                >
                  <X className="w-4 h-4 text-light-text-secondary dark:text-dark-text-secondary" />
                </button>
              </div>
            </div>

            {/* Chat List */}
            <div className="flex-1 overflow-y-auto">
              {chats.length === 0 ? (
                <div className="flex flex-col items-center justify-center h-full p-8 text-center">
                  <MessageSquare className="w-12 h-12 text-light-text-muted dark:text-dark-text-muted mb-4" />
                  <p className="text-light-text-secondary dark:text-dark-text-secondary">
                    {t(locale, 'sidebar.noChats')}
                  </p>
                </div>
              ) : (
                <div className="p-2 space-y-6">
                  {Object.entries(groupedChats).map(([group, groupChats]) => {
                    if (groupChats.length === 0) return null
                    
                    return (
                      <div key={group}>
                        <h3 className="text-xs font-medium text-light-text-muted dark:text-dark-text-muted uppercase tracking-wider px-3 py-2">
                          {t(locale, `sidebar.${group}`)}
                        </h3>
                        <div className="space-y-1">
                          {groupChats.map((chat) => (
                            <motion.div
                              key={chat.id}
                              initial={{ opacity: 0, y: 10 }}
                              animate={{ opacity: 1, y: 0 }}
                              exit={{ opacity: 0, y: -10 }}
                              className="group relative"
                            >
                              <button
                                onClick={() => onSelectChat(chat)}
                                className={`w-full flex items-start space-x-3 p-3 rounded-lg transition-colors text-left ${
                                  currentChat?.id === chat.id
                                    ? 'bg-light-accent dark:bg-dark-accent text-white'
                                    : 'hover:bg-light-tertiary dark:hover:bg-dark-tertiary text-light-text-primary dark:text-dark-text-primary'
                                }`}
                              >
                                <MessageSquare className={`w-4 h-4 mt-0.5 flex-shrink-0 ${
                                  currentChat?.id === chat.id
                                    ? 'text-white'
                                    : 'text-light-text-muted dark:text-dark-text-muted'
                                }`} />
                                <div className="flex-1 min-w-0">
                                  <div className="font-medium text-sm truncate">
                                    {chat.title}
                                  </div>
                                  {chat.messages.length > 0 && (
                                    <div className={`text-xs mt-1 truncate ${
                                      currentChat?.id === chat.id
                                        ? 'text-white/80'
                                        : 'text-light-text-secondary dark:text-dark-text-secondary'
                                    }`}>
                                      {chat.messages[chat.messages.length - 1].content}
                                    </div>
                                  )}
                                  <div className={`text-xs mt-1 ${
                                    currentChat?.id === chat.id
                                      ? 'text-white/60'
                                      : 'text-light-text-muted dark:text-dark-text-muted'
                                  }`}>
                                    {formatTime(chat.updatedAt)}
                                  </div>
                                </div>
                              </button>

                              {/* Delete Button */}
                              <div className={`absolute top-2 ${locale === 'ar' ? 'left-2' : 'right-2'} opacity-0 group-hover:opacity-100 transition-opacity`}>
                                <button
                                  onClick={(e) => handleDeleteChat(chat.id, e)}
                                  className={`p-1.5 rounded-md transition-colors ${
                                    deleteConfirm === chat.id
                                      ? 'bg-red-500 text-white'
                                      : 'bg-light-tertiary dark:bg-dark-tertiary hover:bg-red-100 dark:hover:bg-red-900 text-light-text-secondary dark:text-dark-text-secondary hover:text-red-600 dark:hover:text-red-400'
                                  }`}
                                  aria-label={t(locale, 'a11y.deleteChat')}
                                >
                                  <Trash2 className="w-3 h-3" />
                                </button>
                              </div>
                            </motion.div>
                          ))}
                        </div>
                      </div>
                    )
                  })}
                </div>
              )}
            </div>

            {/* Footer */}
            <div className="p-4 border-t border-light-border dark:border-dark-border">
              <div className="text-xs text-light-text-muted dark:text-dark-text-muted text-center">
                {t(locale, 'footer.poweredBy')} WIDDX AI
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  )
}