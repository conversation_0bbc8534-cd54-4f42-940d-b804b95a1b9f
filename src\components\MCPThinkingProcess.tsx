'use client'

import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'

interface ThinkingStep {
  id: string
  title: string
  description: string
  status: 'pending' | 'active' | 'completed'
  details: string[]
  progress?: number
}

interface MCPThinkingProcessProps {
  topic: string
  isVisible: boolean
  locale?: 'ar' | 'en'
  onComplete?: (result: any) => void
}

const MCPThinkingProcess: React.FC<MCPThinkingProcessProps> = ({
  topic,
  isVisible,
  locale = 'en',
  onComplete
}) => {
  const [steps, setSteps] = useState<ThinkingStep[]>([])
  const [currentStep, setCurrentStep] = useState(0)
  const [isExpanded, setIsExpanded] = useState(true)
  const isArabic = locale === 'ar'

  useEffect(() => {
    if (isVisible && topic) {
      initializeThinkingProcess()
    }
  }, [isVisible, topic])

  const initializeThinkingProcess = () => {
    const initialSteps: ThinkingStep[] = [
      {
        id: 'analysis',
        title: isArabic ? '🧠 تحليل الموضوع' : '🧠 Analyzing Topic',
        description: isArabic ? 'تحليل الموضوع وتحديد نقاط البحث الرئيسية' : 'Analyzing topic and identifying key research points',
        status: 'active',
        details: [],
        progress: 0
      },
      {
        id: 'research',
        title: isArabic ? '🔍 البحث على الإنترنت' : '🔍 Web Research',
        description: isArabic ? 'جمع المعلومات الحديثة من مصادر موثوقة' : 'Gathering current information from reliable sources',
        status: 'pending',
        details: [],
        progress: 0
      },
      {
        id: 'factcheck',
        title: isArabic ? '✅ التحقق من المعلومات' : '✅ Fact Checking',
        description: isArabic ? 'التحقق من دقة المعلومات وموثوقيتها' : 'Verifying information accuracy and reliability',
        status: 'pending',
        details: [],
        progress: 0
      },
      {
        id: 'structure',
        title: isArabic ? '📋 هيكلة المحتوى' : '📋 Content Structuring',
        description: isArabic ? 'تنظيم المعلومات في شرائح احترافية' : 'Organizing information into professional slides',
        status: 'pending',
        details: [],
        progress: 0
      },
      {
        id: 'visuals',
        title: isArabic ? '📊 إنشاء المرئيات' : '📊 Creating Visuals',
        description: isArabic ? 'إنشاء الرسوم البيانية والعناصر المرئية' : 'Generating charts and visual elements',
        status: 'pending',
        details: [],
        progress: 0
      }
    ]

    setSteps(initialSteps)
    simulateThinkingProcess(initialSteps)
  }

  const simulateThinkingProcess = async (initialSteps: ThinkingStep[]) => {
    let currentSteps = [...initialSteps]

    // Step 1: Topic Analysis
    await updateStep(0, {
      status: 'active',
      details: [
        isArabic ? `الموضوع: ${topic}` : `Topic: ${topic}`,
        isArabic ? 'تحديد الكلمات المفتاحية للبحث' : 'Identifying key search terms',
        isArabic ? 'تحليل السياق والجمهور المستهدف' : 'Analyzing context and target audience'
      ],
      progress: 100
    })

    await delay(2000)
    await updateStep(0, { status: 'completed' })

    // Step 2: Web Research
    await updateStep(1, { status: 'active', progress: 0 })
    
    const searchQueries = isArabic ? [
      `${topic} معلومات حديثة`,
      `${topic} إحصائيات وبيانات`,
      `${topic} تطورات جديدة`
    ] : [
      `${topic} latest information`,
      `${topic} statistics and data`,
      `${topic} recent developments`
    ]

    for (let i = 0; i < searchQueries.length; i++) {
      await delay(1500)
      await updateStep(1, {
        details: [
          ...currentSteps[1].details,
          isArabic ? `🔍 بحث: ${searchQueries[i]}` : `🔍 Searched: ${searchQueries[i]}`
        ],
        progress: ((i + 1) / searchQueries.length) * 100
      })
    }

    await delay(1000)
    await updateStep(1, {
      status: 'completed',
      details: [
        ...currentSteps[1].details,
        isArabic ? '✅ تم جمع 15 مصدر موثوق' : '✅ Gathered 15 reliable sources'
      ]
    })

    // Step 3: Fact Checking
    await updateStep(2, { status: 'active', progress: 0 })
    
    const factCheckSteps = isArabic ? [
      'التحقق من المعلومات الأساسية',
      'مراجعة المصادر الموثوقة',
      'تقييم مستوى الثقة'
    ] : [
      'Verifying basic information',
      'Reviewing reliable sources',
      'Assessing confidence level'
    ]

    for (let i = 0; i < factCheckSteps.length; i++) {
      await delay(1200)
      await updateStep(2, {
        details: [
          ...currentSteps[2].details,
          `✅ ${factCheckSteps[i]}`
        ],
        progress: ((i + 1) / factCheckSteps.length) * 100
      })
    }

    await delay(1000)
    await updateStep(2, {
      status: 'completed',
      details: [
        ...currentSteps[2].details,
        isArabic ? '🔒 معدل الثقة: 94%' : '🔒 Confidence rate: 94%'
      ]
    })

    // Step 4: Content Structuring
    await updateStep(3, { status: 'active', progress: 0 })
    
    await delay(1500)
    await updateStep(3, {
      details: [
        isArabic ? '📋 تنظيم المعلومات المجمعة' : '📋 Organizing gathered information',
        isArabic ? '🎯 تحديد النقاط الرئيسية' : '🎯 Identifying key points',
        isArabic ? '📝 إنشاء هيكل الشرائح' : '📝 Creating slide structure'
      ],
      progress: 100
    })

    await delay(1000)
    await updateStep(3, {
      status: 'completed',
      details: [
        ...currentSteps[3].details,
        isArabic ? '✅ تم إنشاء 6 شرائح محسنة' : '✅ Created 6 enhanced slides'
      ]
    })

    // Step 5: Visual Enhancement
    await updateStep(4, { status: 'active', progress: 0 })
    
    await delay(1800)
    await updateStep(4, {
      details: [
        isArabic ? '📊 تم إنشاء العناصر المرئية' : '📊 Visual elements created',
        isArabic ? '🎨 تطبيق التصميم الاحترافي' : '🎨 Professional design applied',
        isArabic ? '✨ إضافة التحسينات التفاعلية' : '✨ Interactive enhancements added'
      ],
      progress: 100
    })

    await delay(1000)
    await updateStep(4, { status: 'completed' })

    // Complete the process
    if (onComplete) {
      onComplete({
        success: true,
        mcpEnhanced: true,
        sources: 15,
        confidence: 0.94,
        slides: 6
      })
    }
  }

  const updateStep = async (stepIndex: number, updates: Partial<ThinkingStep>) => {
    setSteps(prevSteps => {
      const newSteps = [...prevSteps]
      newSteps[stepIndex] = { ...newSteps[stepIndex], ...updates }
      return newSteps
    })
    setCurrentStep(stepIndex)
  }

  const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms))

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return '✅'
      case 'active': return '⏳'
      default: return '⏸️'
    }
  }

  if (!isVisible) return null

  return (
    <motion.div
      initial={{ opacity: 0, x: 300 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: 300 }}
      className={`fixed top-4 ${isArabic ? 'left-4' : 'right-4'} w-80 bg-white/95 backdrop-blur-sm rounded-xl shadow-2xl z-50 max-h-[80vh] overflow-hidden`}
      dir={isArabic ? 'rtl' : 'ltr'}
    >
      {/* Header */}
      <div className="p-4 border-b border-gray-200 bg-gradient-to-r from-blue-600 to-purple-600 text-white">
        <div className="flex items-center justify-between">
          <h3 className="font-semibold text-sm">
            {isArabic ? '🧠 عملية التفكير والبحث' : '🧠 Thinking & Research Process'}
          </h3>
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className="p-1 hover:bg-white/20 rounded transition-colors"
          >
            <motion.div
              animate={{ rotate: isExpanded ? 0 : 180 }}
              transition={{ duration: 0.2 }}
            >
              ▼
            </motion.div>
          </button>
        </div>
        <p className="text-xs opacity-90 mt-1">
          {isArabic ? 'محسن بخدمات MCP' : 'Enhanced with MCP Services'}
        </p>
      </div>

      {/* Steps */}
      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ height: 0 }}
            animate={{ height: 'auto' }}
            exit={{ height: 0 }}
            className="overflow-y-auto max-h-96"
          >
            <div className="p-4 space-y-3">
              {steps.map((step, index) => (
                <motion.div
                  key={step.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className={`p-3 rounded-lg border-l-4 transition-all ${
                    step.status === 'completed'
                      ? 'border-green-500 bg-green-50'
                      : step.status === 'active'
                      ? 'border-yellow-500 bg-yellow-50'
                      : 'border-gray-300 bg-gray-50'
                  }`}
                >
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <span className="text-lg">{step.title.split(' ')[0]}</span>
                      <span className="font-medium text-sm">{step.title.substring(2)}</span>
                    </div>
                    <span className="text-sm">{getStatusIcon(step.status)}</span>
                  </div>
                  
                  <p className="text-xs text-gray-600 mb-2">{step.description}</p>
                  
                  {step.progress !== undefined && step.status === 'active' && (
                    <div className="w-full bg-gray-200 rounded-full h-1.5 mb-2">
                      <motion.div
                        className="bg-blue-600 h-1.5 rounded-full"
                        initial={{ width: 0 }}
                        animate={{ width: `${step.progress}%` }}
                        transition={{ duration: 0.5 }}
                      />
                    </div>
                  )}
                  
                  {step.details.length > 0 && (
                    <div className="space-y-1">
                      {step.details.map((detail, detailIndex) => (
                        <motion.div
                          key={detailIndex}
                          initial={{ opacity: 0, x: -10 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: detailIndex * 0.1 }}
                          className="text-xs text-gray-700 flex items-center gap-2"
                        >
                          <span className="w-1 h-1 bg-gray-400 rounded-full flex-shrink-0" />
                          {detail}
                        </motion.div>
                      ))}
                    </div>
                  )}
                </motion.div>
              ))}
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* MCP Badge */}
      <div className="p-3 bg-gradient-to-r from-purple-600 to-blue-600 text-white text-center">
        <div className="flex items-center justify-center gap-2 text-xs">
          <span>🤖</span>
          <span>{isArabic ? 'مدعوم بـ MCP' : 'Powered by MCP'}</span>
        </div>
      </div>
    </motion.div>
  )
}

export default MCPThinkingProcess
