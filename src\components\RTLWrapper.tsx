'use client'

import { useEffect } from 'react'
import type { Locale } from '@/types'

interface RTLWrapperProps {
  locale: Locale
  children: React.ReactNode
}

export function RTLWrapper({ locale, children }: RTLWrapperProps) {
  useEffect(() => {
    // Update document direction and language
    document.documentElement.lang = locale
    document.documentElement.dir = locale === 'ar' ? 'rtl' : 'ltr'
    
    // Add RTL class to body for additional styling
    if (locale === 'ar') {
      document.body.classList.add('rtl')
    } else {
      document.body.classList.remove('rtl')
    }
  }, [locale])

  return (
    <div className={`${locale === 'ar' ? 'rtl' : 'ltr'}`} dir={locale === 'ar' ? 'rtl' : 'ltr'}>
      {children}
    </div>
  )
}
