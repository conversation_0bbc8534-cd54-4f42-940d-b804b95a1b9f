'use client'

import { useState, useRef, useEffect } from 'react'
import { motion } from 'framer-motion'
import TextareaAutosize from 'react-textarea-autosize'
import { Send, Paperclip, Mic, Square } from 'lucide-react'
import { t } from '@/locales'
import type { ChatInputProps } from '@/types'

export function ChatInput({
  onSendMessage,
  disabled = false,
  placeholder = '',
  maxLength = 4000,
  currentMode,
  locale,
}: ChatInputProps) {
  const [message, setMessage] = useState('')
  const [isRecording, setIsRecording] = useState(false)
  const textareaRef = useRef<HTMLTextAreaElement>(null)

  // Focus textarea when component mounts
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.focus()
    }
  }, [])

  // Handle keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Focus input with Cmd/Ctrl + L
      if ((e.metaKey || e.ctrlKey) && e.key === 'l') {
        e.preventDefault()
        textareaRef.current?.focus()
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [])

  const handleSubmit = async (e?: React.FormEvent) => {
    e?.preventDefault()
    
    const trimmedMessage = message.trim()
    if (!trimmedMessage || disabled || trimmedMessage.length > maxLength) {
      return
    }

    setMessage('')
    await onSendMessage(trimmedMessage)
    
    // Refocus textarea after sending
    setTimeout(() => {
      textareaRef.current?.focus()
    }, 100)
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSubmit()
    }
  }

  const handleFileUpload = () => {
    // File upload feature coming soon
    const message = locale === 'ar'
      ? 'ميزة رفع الملفات قريباً'
      : 'File upload feature coming soon'
    alert(message)
  }

  const toggleRecording = () => {
    // Voice recording feature coming soon
    const message = locale === 'ar'
      ? 'ميزة التسجيل الصوتي قريباً'
      : 'Voice recording feature coming soon'
    alert(message)
  }

  const canSend = message.trim().length > 0 && !disabled && message.length <= maxLength
  const isOverLimit = message.length > maxLength

  return (
    <div className="relative">
      <form onSubmit={handleSubmit} className="relative">
        <div className={`relative bg-light-primary dark:bg-dark-primary border-2 rounded-2xl transition-all duration-200 ${
          disabled
            ? 'border-light-border dark:border-dark-border opacity-50'
            : 'border-light-border dark:border-dark-border focus-within:border-light-accent dark:focus-within:border-dark-accent'
        }`}>
          {/* Input Area */}
          <div className="flex items-end gap-2 sm:gap-3 p-2 sm:p-4 mobile-padding">
            {/* Attachment Button */}
            <button
              type="button"
              onClick={handleFileUpload}
              disabled={disabled}
              className="flex-shrink-0 p-1.5 sm:p-2 rounded-lg hover:bg-light-tertiary dark:hover:bg-dark-tertiary transition-colors disabled:opacity-50 disabled:cursor-not-allowed touch-target"
              aria-label={t(locale, 'files.upload')}
            >
              <Paperclip className="w-4 h-4 sm:w-5 sm:h-5 text-light-text-secondary dark:text-dark-text-secondary" />
            </button>

            {/* Text Input */}
            <div className="flex-1 relative">
              <TextareaAutosize
                ref={textareaRef}
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                onKeyDown={handleKeyDown}
                placeholder={placeholder || t(locale, 'chat.inputPlaceholder')}
                disabled={disabled}
                maxRows={8}
                minRows={1}
                className={`w-full bg-transparent border-none outline-none resize-none text-light-text-primary dark:text-dark-text-primary placeholder-light-text-muted dark:placeholder-dark-text-muted ${
                  locale === 'ar' ? 'text-right' : 'text-left'
                }`}
                style={{
                  fontSize: '16px', // Prevent zoom on iOS
                  lineHeight: '1.5',
                }}
              />
              
              {/* Character Count */}
              <div className={`absolute -bottom-6 ${locale === 'ar' ? 'left-0' : 'right-0'} text-xs ${
                isOverLimit
                  ? 'text-red-500'
                  : 'text-light-text-muted dark:text-dark-text-muted'
              }`}>
                {message.length}/{maxLength}
              </div>
            </div>

            {/* Voice Recording Button */}
            <button
              type="button"
              onClick={toggleRecording}
              disabled={disabled}
              className={`flex-shrink-0 p-2 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed ${
                isRecording
                  ? 'bg-red-500 text-white hover:bg-red-600'
                  : 'hover:bg-light-tertiary dark:hover:bg-dark-tertiary text-light-text-secondary dark:text-dark-text-secondary'
              }`}
              aria-label={isRecording ? 'Stop recording' : 'Start voice recording'}
            >
              {isRecording ? (
                <Square className="w-5 h-5" />
              ) : (
                <Mic className="w-5 h-5" />
              )}
            </button>

            {/* Send Button */}
            <motion.button
              type="submit"
              disabled={!canSend}
              className={`flex-shrink-0 p-2 rounded-lg transition-all duration-200 ${
                canSend
                  ? 'bg-light-accent dark:bg-dark-accent hover:bg-light-accent-hover dark:hover:bg-dark-accent-hover text-white shadow-md hover:shadow-lg'
                  : 'bg-light-border dark:bg-dark-border text-light-text-muted dark:text-dark-text-muted cursor-not-allowed'
              }`}
              whileHover={canSend ? { scale: 1.05 } : {}}
              whileTap={canSend ? { scale: 0.95 } : {}}
              aria-label={t(locale, 'a11y.sendMessage')}
            >
              <Send className="w-5 h-5" />
            </motion.button>
          </div>
        </div>
      </form>

      {/* Mode Indicator */}
      <div className="mt-2 flex items-center justify-between text-xs text-light-text-muted dark:text-dark-text-muted">
        <div className="flex items-center gap-2">
          <div className="w-2 h-2 bg-light-accent dark:bg-dark-accent rounded-full"></div>
          <span>{locale === 'ar' ? `وضع ${t(locale, `modes.${currentMode}.name`)}` : `${t(locale, `modes.${currentMode}.name`)} mode`}</span>
        </div>
        
        {/* Keyboard Shortcut Hint */}
        <div className="hidden sm:flex items-center gap-1">
          <kbd className="px-1.5 py-0.5 bg-light-tertiary dark:bg-dark-tertiary rounded text-xs">
            {navigator.platform.includes('Mac') ? '⌘' : 'Ctrl'}
          </kbd>
          <span>+</span>
          <kbd className="px-1.5 py-0.5 bg-light-tertiary dark:bg-dark-tertiary rounded text-xs">
            Enter
          </kbd>
          <span className="ml-1">{locale === 'ar' ? 'للإرسال' : 'to send'}</span>
        </div>
      </div>
    </div>
  )
}