# AI Integration Documentation

## Supported Models

### DeepSeek Models
- `deepseek-chat`: General purpose chat model (default)
- `deepseek-coder`: Specialized for code generation and analysis

### Gemini Models
- `gemini-1.5-flash`: Fast general purpose model
- `gemini-pro-vision`: Supports multimodal input

## Model Selection
Models are selected based on chat mode:
- **chat**: `deepseek-chat`
- **code**: `deepseek-coder` 
- **slides**: `deepseek-chat`
- **writer/research/summarizer**: `deepseek-chat`

## Key Features

### Fallback System
- Automatic fallback to alternative provider if primary fails
- Fallback responses for when both providers are unavailable
- Timeout handling (25 seconds)

### Rate Limiting
- Configurable via `RATE_LIMIT_REQUESTS_PER_MINUTE` (default: 60)
- Per-user tracking
- Automatic reset every minute

### Cost Calculation
- Token-based cost estimation
- Different rates per model:
  - DeepSeek: $0.00014 per token
  - Gemini Flash: $0.0005 per token
  - Gemini Pro Vision: $0.0025 per token

### Streaming Support
- Both providers support streaming
- Special handling for Server-Sent Events (SSE)
- Fallback to non-streaming if streaming fails

## Configuration
Requires environment variables:
- `DEEPSEEK_API_KEY`
- `GEMINI_API_KEY`
- `DEEPSEEK_API_URL` (optional)
- `GEMINI_API_URL` (optional)
- `MAX_TOKENS` (default: 4000)
- `TEMPERATURE` (default: 0.7)

## Health Checking
- `/api/health` endpoint checks both services
- Returns detailed status including:
  - Service availability
  - Configuration status
  - Error messages