import { NextRequest, NextResponse } from 'next/server'
import { mcpIntegrationService } from '@/lib/mcp-integration-service'
import { mcpServiceManager } from '@/lib/mcp-service-manager'
import { advancedAIManager } from '@/lib/advanced-ai-manager'
import { chartGeneratorService } from '@/lib/chart-generator'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { 
      topic,
      template = 'arabic-professional',
      mcpServices = ['research', 'fact-check', 'charts', 'time'],
      locale = 'ar'
    } = body

    if (!topic) {
      return NextResponse.json(
        { error: 'Topic is required' },
        { status: 400 }
      )
    }

    console.log('🚀 Starting comprehensive MCP-enhanced presentation generation');
    console.log('📋 Topic:', topic);
    console.log('🔧 Requested MCP services:', mcpServices);

    const startTime = Date.now();

    // Step 1: Gather MCP enhancements
    console.log('🔍 Step 1: Gathering MCP enhancements...');
    const mcpEnhancements = await mcpIntegrationService.enhancePresentationContent(topic);

    // Step 2: Generate enhanced charts if requested
    let enhancedCharts: any[] = [];
    if (mcpServices.includes('charts')) {
      console.log('📊 Step 2: Generating MCP-enhanced charts...');
      const baseCharts = chartGeneratorService.generateChartsForTopic(topic);
      
      enhancedCharts = await Promise.all(
        baseCharts.map(async (chart) => {
          const mcpChart = await chartGeneratorService.generateMCPChart(chart);
          return {
            ...chart,
            mcpEnhanced: mcpChart.success,
            mcpUrl: mcpChart.success ? mcpChart.data.url : null,
            mcpSource: mcpChart.source
          };
        })
      );
    }

    // Step 3: Generate intelligent presentation
    console.log('🧠 Step 3: Generating intelligent presentation...');
    const presentation = await advancedAIManager.generateAdvancedPresentation(topic);

    // Step 4: Enhance presentation with MCP data
    console.log('✨ Step 4: Integrating MCP enhancements...');
    const enhancedPresentation = await enhancePresentationWithMCP(
      presentation,
      mcpEnhancements,
      enhancedCharts,
      mcpServices
    );

    const processingTime = Date.now() - startTime;

    // Step 5: Prepare response
    const response = {
      success: true,
      data: {
        content: enhancedPresentation,
        model: 'mcp-intelligent-system',
        provider: 'widdx-mcp',
        tokens: calculateTokens(topic, mcpEnhancements),
        processingTime,
        cost: 0, // Free MCP services
        contentType: detectContentType(topic),
        slideCount: countSlides(enhancedPresentation),
        mcpEnhanced: true,
        mcpServices: mcpEnhancements.mcpServices,
        mcpData: {
          researchSources: mcpEnhancements.researchData?.data?.length || 0,
          factCheckConfidence: mcpEnhancements.factCheckData?.confidence || 0,
          chartsGenerated: enhancedCharts.length,
          timeData: !!mcpEnhancements.timeData,
          servicesUsed: mcpEnhancements.mcpServices.length
        },
        performance: {
          totalTime: processingTime,
          mcpTime: processingTime * 0.3, // Estimated MCP processing time
          generationTime: processingTime * 0.7
        }
      }
    };

    console.log('✅ MCP-enhanced presentation generated successfully');
    console.log('📊 Performance:', response.data.performance);
    console.log('🔗 MCP services used:', response.data.mcpServices);

    return NextResponse.json(response);

  } catch (error) {
    console.error('❌ MCP presentation generation failed:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'MCP presentation generation failed',
        mcpError: true,
        fallbackAvailable: true
      },
      { status: 500 }
    )
  }
}

export async function GET() {
  // Get MCP service status
  const mcpStatus = mcpServiceManager.getServiceStatus();
  
  return NextResponse.json({
    message: 'WIDDX MCP-Enhanced Presentation API',
    version: '1.0.0',
    mcpServices: mcpStatus,
    availableServices: [
      'research - Web search and content research',
      'fact-check - Fact verification and validation',
      'charts - Data visualization generation',
      'time - Time and date formatting',
      'content-fetch - External content retrieval'
    ],
    endpoints: {
      generate: 'POST /api/mcp-presentation',
      status: 'GET /api/mcp-presentation'
    }
  });
}

// Helper functions

async function enhancePresentationWithMCP(
  presentation: string,
  mcpEnhancements: any,
  enhancedCharts: any[],
  requestedServices: string[]
): Promise<string> {
  let enhanced = presentation;

  // Add MCP enhancement indicators
  const mcpIndicator = `
    <div id="mcp-enhancement-panel" style="position: fixed; top: 10px; right: 10px; background: linear-gradient(135deg, #10B981, #059669); color: white; padding: 12px 16px; border-radius: 8px; font-size: 12px; z-index: 1000; box-shadow: 0 4px 12px rgba(0,0,0,0.15);">
      <div style="font-weight: bold; margin-bottom: 4px;">🔗 MCP Enhanced</div>
      <div style="font-size: 10px; opacity: 0.9;">
        ${mcpEnhancements.mcpServices.join(' • ')}
      </div>
    </div>
  `;

  // Add research data panel if available
  if (mcpEnhancements.researchData && requestedServices.includes('research')) {
    const researchPanel = `
      <div id="research-panel" style="position: fixed; bottom: 10px; left: 10px; background: rgba(59, 130, 246, 0.9); color: white; padding: 10px; border-radius: 6px; font-size: 11px; max-width: 300px; z-index: 1000;">
        <div style="font-weight: bold; margin-bottom: 4px;">🔍 Research Sources</div>
        <div style="font-size: 10px;">
          ${mcpEnhancements.researchData.data?.slice(0, 2).map((source: any) => 
            `• ${source.title}`
          ).join('<br>') || 'Research data available'}
        </div>
      </div>
    `;
    enhanced = enhanced.replace('</body>', researchPanel + '</body>');
  }

  // Add fact-check confidence indicator
  if (mcpEnhancements.factCheckData && requestedServices.includes('fact-check')) {
    const confidence = mcpEnhancements.factCheckData.confidence || 0;
    const confidenceColor = confidence > 0.8 ? '#10B981' : confidence > 0.6 ? '#F59E0B' : '#EF4444';
    
    const factCheckPanel = `
      <div id="fact-check-panel" style="position: fixed; bottom: 10px; right: 10px; background: ${confidenceColor}; color: white; padding: 8px 12px; border-radius: 6px; font-size: 11px; z-index: 1000;">
        ✓ Fact-checked (${Math.round(confidence * 100)}%)
      </div>
    `;
    enhanced = enhanced.replace('</body>', factCheckPanel + '</body>');
  }

  // Add time data if available
  if (mcpEnhancements.timeData && requestedServices.includes('time')) {
    const timePanel = `
      <div id="time-panel" style="position: fixed; top: 10px; left: 10px; background: rgba(139, 92, 246, 0.9); color: white; padding: 8px 12px; border-radius: 6px; font-size: 11px; z-index: 1000;">
        🕒 ${mcpEnhancements.timeData.formatted}
      </div>
    `;
    enhanced = enhanced.replace('</body>', timePanel + '</body>');
  }

  // Add main MCP indicator
  enhanced = enhanced.replace('</body>', mcpIndicator + '</body>');

  return enhanced;
}

function calculateTokens(topic: string, mcpEnhancements: any): number {
  let tokens = topic.length / 4; // Base tokens for topic
  
  if (mcpEnhancements.researchData) tokens += 50;
  if (mcpEnhancements.factCheckData) tokens += 30;
  if (mcpEnhancements.visualAssets) tokens += 20;
  if (mcpEnhancements.timeData) tokens += 10;
  
  return Math.round(tokens);
}

function detectContentType(topic: string): string {
  const topicLower = topic.toLowerCase();
  
  if (topicLower.includes('business') || topicLower.includes('أعمال')) return 'business';
  if (topicLower.includes('education') || topicLower.includes('تعليم')) return 'education';
  if (topicLower.includes('technology') || topicLower.includes('تقنية')) return 'technology';
  if (topicLower.includes('health') || topicLower.includes('صحة')) return 'health';
  if (topicLower.includes('palestine') || topicLower.includes('فلسطين')) return 'research-palestine';
  
  return 'general';
}

function countSlides(presentation: string): number {
  const slideMatches = presentation.match(/<div[^>]*class[^>]*slide[^>]*>/gi);
  return slideMatches ? slideMatches.length : 0;
}
