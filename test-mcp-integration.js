/**
 * Test script for WIDDX MCP Integration
 * Demonstrates the enhanced presentation capabilities with free MCP services
 */

const API_BASE = 'http://localhost:3000/api';

async function testMCPIntegration() {
  console.log('🚀 Testing WIDDX MCP Integration');
  console.log('=' .repeat(50));

  // Test 1: Check MCP Service Status
  console.log('\n📊 Test 1: MCP Service Status');
  try {
    const response = await fetch(`${API_BASE}/mcp-presentation`);
    const data = await response.json();
    
    console.log('✅ MCP Services Available:');
    Object.entries(data.mcpServices).forEach(([name, service]) => {
      console.log(`   • ${name}: ${service.capabilities.join(', ')}`);
    });
  } catch (error) {
    console.error('❌ MCP Status Test Failed:', error.message);
  }

  // Test 2: Enhanced Fallback Presentation with MCP
  console.log('\n📝 Test 2: MCP-Enhanced Fallback Presentation');
  try {
    const response = await fetch(`${API_BASE}/test-fallback`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        topic: 'AI-powered sustainable energy solutions for smart cities',
        template: 'arabic-professional',
        enableMCP: true
      })
    });
    
    const data = await response.json();
    
    if (data.success) {
      console.log('✅ MCP-Enhanced Presentation Generated:');
      console.log(`   • Model: ${data.data.model}`);
      console.log(`   • Slide Count: ${data.data.slideCount}`);
      console.log(`   • MCP Enhanced: ${data.data.mcpEnhanced}`);
      console.log(`   • MCP Services Used: ${data.data.mcpServices?.join(', ') || 'None'}`);
      console.log(`   • Processing Time: ${data.data.processingTime}ms`);
      
      if (data.data.mcpData) {
        console.log('   • MCP Data:');
        console.log(`     - Research Data: ${data.data.mcpData.hasResearchData ? '✅' : '❌'}`);
        console.log(`     - Fact Check Data: ${data.data.mcpData.hasFactCheckData ? '✅' : '❌'}`);
        console.log(`     - Visual Assets: ${data.data.mcpData.hasVisualAssets ? '✅' : '❌'}`);
        console.log(`     - Time Data: ${data.data.mcpData.hasTimeData ? '✅' : '❌'}`);
      }
    } else {
      console.error('❌ Enhanced Fallback Failed:', data.error);
    }
  } catch (error) {
    console.error('❌ Enhanced Fallback Test Failed:', error.message);
  }

  // Test 3: Full MCP-Enhanced Presentation
  console.log('\n🧠 Test 3: Full MCP-Enhanced Intelligent Presentation');
  try {
    const response = await fetch(`${API_BASE}/mcp-presentation`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        topic: 'Blockchain technology for supply chain transparency in the food industry',
        template: 'arabic-professional',
        mcpServices: ['research', 'fact-check', 'charts', 'time'],
        locale: 'en'
      })
    });
    
    const data = await response.json();
    
    if (data.success) {
      console.log('✅ Full MCP-Enhanced Presentation Generated:');
      console.log(`   • Model: ${data.data.model}`);
      console.log(`   • Provider: ${data.data.provider}`);
      console.log(`   • Slide Count: ${data.data.slideCount}`);
      console.log(`   • Content Type: ${data.data.contentType}`);
      console.log(`   • MCP Services: ${data.data.mcpServices?.join(', ') || 'None'}`);
      console.log(`   • Processing Time: ${data.data.processingTime}ms`);
      
      if (data.data.mcpData) {
        console.log('   • MCP Enhancement Details:');
        console.log(`     - Research Sources: ${data.data.mcpData.researchSources}`);
        console.log(`     - Fact Check Confidence: ${Math.round(data.data.mcpData.factCheckConfidence * 100)}%`);
        console.log(`     - Charts Generated: ${data.data.mcpData.chartsGenerated}`);
        console.log(`     - Services Used: ${data.data.mcpData.servicesUsed}`);
      }
      
      if (data.data.performance) {
        console.log('   • Performance Metrics:');
        console.log(`     - Total Time: ${data.data.performance.totalTime}ms`);
        console.log(`     - MCP Time: ${Math.round(data.data.performance.mcpTime)}ms`);
        console.log(`     - Generation Time: ${Math.round(data.data.performance.generationTime)}ms`);
      }
    } else {
      console.error('❌ Full MCP Enhancement Failed:', data.error);
    }
  } catch (error) {
    console.error('❌ Full MCP Test Failed:', error.message);
  }

  // Test 4: Arabic Business Presentation with MCP
  console.log('\n🌍 Test 4: Arabic Business Presentation with MCP');
  try {
    const response = await fetch(`${API_BASE}/mcp-presentation`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        topic: 'استراتيجية التحول الرقمي للشركات الناشئة في المملكة العربية السعودية',
        template: 'arabic-professional',
        mcpServices: ['research', 'fact-check', 'charts', 'time'],
        locale: 'ar'
      })
    });
    
    const data = await response.json();
    
    if (data.success) {
      console.log('✅ Arabic MCP-Enhanced Presentation Generated:');
      console.log(`   • Model: ${data.data.model}`);
      console.log(`   • Slide Count: ${data.data.slideCount}`);
      console.log(`   • Content Type: ${data.data.contentType}`);
      console.log(`   • MCP Services: ${data.data.mcpServices?.join(', ') || 'None'}`);
      console.log(`   • Cultural Context: Arabic`);
      console.log(`   • Processing Time: ${data.data.processingTime}ms`);
    } else {
      console.error('❌ Arabic MCP Enhancement Failed:', data.error);
    }
  } catch (error) {
    console.error('❌ Arabic MCP Test Failed:', error.message);
  }

  // Test 5: Performance Comparison
  console.log('\n⚡ Test 5: Performance Comparison (MCP vs Standard)');
  
  const testTopic = 'Machine learning applications in healthcare diagnostics';
  
  try {
    // Standard presentation
    const startStandard = Date.now();
    const standardResponse = await fetch(`${API_BASE}/test-fallback`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        topic: testTopic,
        enableMCP: false
      })
    });
    const standardTime = Date.now() - startStandard;
    const standardData = await standardResponse.json();

    // MCP-enhanced presentation
    const startMCP = Date.now();
    const mcpResponse = await fetch(`${API_BASE}/test-fallback`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        topic: testTopic,
        enableMCP: true
      })
    });
    const mcpTime = Date.now() - startMCP;
    const mcpData = await mcpResponse.json();

    console.log('📊 Performance Comparison Results:');
    console.log(`   • Standard Generation: ${standardTime}ms`);
    console.log(`   • MCP-Enhanced Generation: ${mcpTime}ms`);
    console.log(`   • Performance Overhead: ${mcpTime - standardTime}ms (${Math.round(((mcpTime - standardTime) / standardTime) * 100)}%)`);
    console.log(`   • Standard Slides: ${standardData.success ? standardData.data.slideCount : 'Failed'}`);
    console.log(`   • MCP-Enhanced Slides: ${mcpData.success ? mcpData.data.slideCount : 'Failed'}`);
    console.log(`   • MCP Services Used: ${mcpData.success ? mcpData.data.mcpServices?.length || 0 : 0}`);
    
  } catch (error) {
    console.error('❌ Performance Comparison Failed:', error.message);
  }

  console.log('\n🎉 MCP Integration Testing Complete!');
  console.log('=' .repeat(50));
}

// Run the tests
testMCPIntegration().catch(console.error);
