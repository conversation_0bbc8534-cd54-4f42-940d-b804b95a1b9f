import type { Message } from '@/types'

export interface GeminiMessage {
  role: 'user' | 'model'
  parts: { text: string }[]
}

export interface GeminiResponse {
  candidates: {
    content: {
      parts: { text: string }[]
      role: string
    }
    finishReason: string
    index: number
    safetyRatings: any[]
  }[]
  promptFeedback: {
    safetyRatings: any[]
  }
}

export class GeminiService {
  private apiKey: string
  private baseUrl: string

  constructor() {
    this.apiKey = process.env.GEMINI_API_KEY || ''
    this.baseUrl = process.env.GEMINI_API_URL || 'https://generativelanguage.googleapis.com/v1beta'
    
    if (!this.apiKey) {
      // GEMINI_API_KEY is not configured. GeminiService will operate in no-op mode.
      // No throw; allow fallback
    }
  }

  async generateResponse(
    messages: Message[],
    systemPrompt: string,
    model: string = 'gemini-1.5-flash',
    stream: boolean = false
  ): Promise<string | ReadableStream> {
    if (!this.apiKey) {
      throw new Error('Gemini API key is not configured')
    }
    // Convert messages to Gemini format
    const geminiMessages: GeminiMessage[] = []
    
    // Add system prompt as first user message
    if (systemPrompt) {
      geminiMessages.push({
        role: 'user',
        parts: [{ text: systemPrompt }]
      })
      geminiMessages.push({
        role: 'model',
        parts: [{ text: 'I understand. I am WIDDX AI, and I will assist you according to these guidelines.' }]
      })
    }

    // Convert chat messages
    messages.forEach(msg => {
      geminiMessages.push({
        role: msg.role === 'user' ? 'user' : 'model',
        parts: [{ text: msg.content }]
      })
    })

    const requestBody = {
      contents: geminiMessages,
      generationConfig: {
        temperature: parseFloat(process.env.TEMPERATURE || '0.7'),
        topK: 40,
        topP: 0.95,
        maxOutputTokens: parseInt(process.env.MAX_TOKENS || '4000'),
      },
      safetySettings: [
        {
          category: 'HARM_CATEGORY_HARASSMENT',
          threshold: 'BLOCK_MEDIUM_AND_ABOVE'
        },
        {
          category: 'HARM_CATEGORY_HATE_SPEECH',
          threshold: 'BLOCK_MEDIUM_AND_ABOVE'
        },
        {
          category: 'HARM_CATEGORY_SEXUALLY_EXPLICIT',
          threshold: 'BLOCK_MEDIUM_AND_ABOVE'
        },
        {
          category: 'HARM_CATEGORY_DANGEROUS_CONTENT',
          threshold: 'BLOCK_MEDIUM_AND_ABOVE'
        }
      ]
    }

    const endpoint = stream 
      ? `${this.baseUrl}/models/${model}:streamGenerateContent`
      : `${this.baseUrl}/models/${model}:generateContent`

    try {
      const response = await fetch(`${endpoint}?key=${this.apiKey}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'WIDDX-AI/1.0'
        },
        body: JSON.stringify(requestBody)
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(`Gemini API error: ${response.status} - ${errorData.error?.message || 'Unknown error'}`)
      }

      if (stream) {
        return this.handleStreamResponse(response)
      } else {
        const data: GeminiResponse = await response.json()
        const content = data.candidates?.[0]?.content?.parts?.[0]?.text
        return content || 'No response generated'
      }
    } catch (error) {
      console.error('Gemini API Error:', error)
      throw new Error(`Failed to generate response: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  private async handleStreamResponse(response: Response): Promise<ReadableStream> {
    const reader = response.body?.getReader()
    if (!reader) {
      throw new Error('No response body available')
    }

    return new ReadableStream({
      async start(controller) {
        const decoder = new TextDecoder()
        
        try {
          while (true) {
            const { done, value } = await reader.read()
            
            if (done) {
              controller.close()
              break
            }

            const chunk = decoder.decode(value, { stream: true })
            const lines = chunk.split('\n')

            for (const line of lines) {
              if (line.trim() && line.startsWith('{')) {
                try {
                  const parsed = JSON.parse(line)
                  const content = parsed.candidates?.[0]?.content?.parts?.[0]?.text
                  
                  if (content) {
                    controller.enqueue(new TextEncoder().encode(content))
                  }
                } catch (e) {
                  // Skip invalid JSON
                  continue
                }
              }
            }
          }
        } catch (error) {
          controller.error(error)
        }
      }
    })
  }

  async checkHealth(): Promise<boolean> {
    if (!this.apiKey) {
      return false
    }

    try {
      const response = await fetch(`${this.baseUrl}/models/gemini-1.5-flash?key=${this.apiKey}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        }
      })
      return response.ok
    } catch {
      return false
    }
  }
}