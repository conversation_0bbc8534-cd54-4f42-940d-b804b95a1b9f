// Advanced Chart Generator for AI Slides with MCP Integration

import { mcpServiceManager, type ChartConfig, type MCPResponse } from './mcp-service-manager'

interface ChartData {
  type: 'bar' | 'line' | 'pie' | 'doughnut' | 'radar' | 'area' | 'timeline'
  title: string
  data: {
    labels: string[]
    datasets: {
      label: string
      data: number[]
      backgroundColor?: string | string[]
      borderColor?: string | string[]
      borderWidth?: number
      fill?: boolean
      tension?: number
      pointBackgroundColor?: string
    }[]
  }
  options: any
  description: string
}

interface TopicData {
  [key: string]: {
    charts: ChartData[]
    statistics: { [key: string]: number | string }
  }
}

class ChartGeneratorService {
  private readonly topicData: TopicData = {
    'فلسطين': {
      charts: [
        {
          type: 'bar',
          title: 'المساحة مقارنة بالدول المجاورة (كم²)',
          data: {
            labels: ['فلسطين', 'لبنان', 'الأردن', 'الكويت', 'قطر'],
            datasets: [{
              label: 'المساحة (كم²)',
              data: [27000, 10452, 89342, 17818, 11586],
              backgroundColor: ['#2E8B57', '#4682B4', '#DAA520', '#CD853F', '#9370DB'],
              borderColor: '#2c3e50',
              borderWidth: 2
            }]
          },
          options: {
            responsive: true,
            plugins: {
              legend: { display: false },
              title: { display: true, text: 'مقارنة المساحات الجغرافية' }
            },
            scales: {
              y: { beginAtZero: true, title: { display: true, text: 'المساحة (كم²)' } }
            }
          },
          description: 'مقارنة مساحة فلسطين مع الدول المجاورة في المنطقة'
        },
        {
          type: 'pie',
          title: 'التوزيع السكاني حسب المناطق',
          data: {
            labels: ['الضفة الغربية', 'قطاع غزة', 'القدس الشرقية'],
            datasets: [{
              label: 'عدد السكان',
              data: [3100000, 2300000, 350000],
              backgroundColor: ['#FF6384', '#36A2EB', '#FFCE56'],
              borderWidth: 2,
              borderColor: '#fff'
            }]
          },
          options: {
            responsive: true,
            plugins: {
              legend: { position: 'bottom' },
              title: { display: true, text: 'التوزيع السكاني' }
            }
          },
          description: 'توزيع السكان الفلسطينيين عبر المناطق الرئيسية'
        },
        {
          type: 'doughnut',
          title: 'القطاعات الاقتصادية الرئيسية',
          data: {
            labels: ['الزراعة', 'الصناعة', 'الخدمات', 'التجارة', 'السياحة'],
            datasets: [{
              label: 'المساهمة في الاقتصاد (%)',
              data: [25, 20, 30, 15, 10],
              backgroundColor: ['#4CAF50', '#2196F3', '#FF9800', '#9C27B0', '#F44336'],
              borderWidth: 3,
              borderColor: '#fff'
            }]
          },
          options: {
            responsive: true,
            plugins: {
              legend: { position: 'right' },
              title: { display: true, text: 'الاقتصاد الفلسطيني' }
            }
          },
          description: 'توزيع القطاعات الاقتصادية ومساهمتها في الاقتصاد الفلسطيني'
        }
      ],
      statistics: {
        'المساحة الإجمالية': '27,000 كم²',
        'عدد السكان': '5.75 مليون نسمة',
        'العاصمة': 'القدس الشرقية',
        'اللغة الرسمية': 'العربية'
      }
    },
    'تكنولوجيا': {
      charts: [
        {
          type: 'line',
          title: 'نمو الذكاء الاصطناعي عبر السنوات',
          data: {
            labels: ['2018', '2019', '2020', '2021', '2022', '2023', '2024'],
            datasets: [{
              label: 'الاستثمار (مليار دولار)',
              data: [12, 18, 25, 35, 50, 75, 95],
              borderColor: '#3B82F6',
              backgroundColor: 'rgba(59, 130, 246, 0.1)',
              borderWidth: 3,
              fill: true,
              tension: 0.4
            }]
          },
          options: {
            responsive: true,
            plugins: {
              legend: { display: true },
              title: { display: true, text: 'نمو الاستثمار في الذكاء الاصطناعي' }
            },
            scales: {
              y: { beginAtZero: true, title: { display: true, text: 'مليار دولار' } }
            }
          },
          description: 'تطور الاستثمارات في مجال الذكاء الاصطناعي خلال السنوات الأخيرة'
        },
        {
          type: 'radar',
          title: 'مهارات التكنولوجيا المطلوبة',
          data: {
            labels: ['البرمجة', 'تحليل البيانات', 'الذكاء الاصطناعي', 'الأمن السيبراني', 'التطوير السحابي'],
            datasets: [{
              label: 'مستوى الطلب',
              data: [90, 85, 95, 80, 75],
              backgroundColor: 'rgba(34, 197, 94, 0.2)',
              borderColor: '#22C55E',
              borderWidth: 2,
              pointBackgroundColor: '#22C55E'
            }]
          },
          options: {
            responsive: true,
            plugins: {
              legend: { display: true },
              title: { display: true, text: 'المهارات التقنية الأكثر طلباً' }
            },
            scales: {
              r: { beginAtZero: true, max: 100 }
            }
          },
          description: 'تحليل المهارات التقنية الأكثر طلباً في سوق العمل'
        }
      ],
      statistics: {
        'نمو القطاع سنوياً': '15%',
        'عدد الوظائف المتاحة': '2.3 مليون',
        'متوسط الراتب': '$85,000',
        'أسرع المجالات نمواً': 'الذكاء الاصطناعي'
      }
    },
    'تعليم': {
      charts: [
        {
          type: 'bar',
          title: 'معدلات التعليم حسب المراحل',
          data: {
            labels: ['الابتدائي', 'المتوسط', 'الثانوي', 'الجامعي', 'الدراسات العليا'],
            datasets: [{
              label: 'معدل الالتحاق (%)',
              data: [98, 92, 85, 65, 25],
              backgroundColor: ['#10B981', '#3B82F6', '#8B5CF6', '#F59E0B', '#EF4444'],
              borderColor: '#374151',
              borderWidth: 1
            }]
          },
          options: {
            responsive: true,
            plugins: {
              legend: { display: false },
              title: { display: true, text: 'معدلات الالتحاق بالتعليم' }
            },
            scales: {
              y: { beginAtZero: true, max: 100, title: { display: true, text: 'النسبة المئوية' } }
            }
          },
          description: 'معدلات الالتحاق بالتعليم في مختلف المراحل الدراسية'
        }
      ],
      statistics: {
        'معدل محو الأمية': '95%',
        'عدد الجامعات': '150 جامعة',
        'نسبة الخريجين': '78%',
        'الاستثمار في التعليم': '6% من الناتج المحلي'
      }
    }
  }

  /**
   * Generate charts based on topic
   */
  generateChartsForTopic(topic: string): ChartData[] {
    const topicLower = topic.toLowerCase()
    
    // Find matching topic data
    for (const [key, data] of Object.entries(this.topicData)) {
      if (topicLower.includes(key.toLowerCase()) || 
          (key === 'فلسطين' && (topicLower.includes('palestine') || topicLower.includes('فلسطين'))) ||
          (key === 'تكنولوجيا' && (topicLower.includes('technology') || topicLower.includes('ai'))) ||
          (key === 'تعليم' && (topicLower.includes('education') || topicLower.includes('learning')))) {
        return data.charts
      }
    }

    // Generate generic charts if no specific data found
    return this.generateGenericCharts(topic)
  }

  /**
   * Generate generic charts for unknown topics
   */
  private generateGenericCharts(topic: string): ChartData[] {
    return [
      {
        type: 'bar',
        title: `تحليل ${topic} - البيانات الرئيسية`,
        data: {
          labels: ['المعيار الأول', 'المعيار الثاني', 'المعيار الثالث', 'المعيار الرابع'],
          datasets: [{
            label: 'القيم',
            data: [75, 85, 65, 90],
            backgroundColor: ['#3B82F6', '#10B981', '#F59E0B', '#EF4444'],
            borderColor: '#374151',
            borderWidth: 2
          }]
        },
        options: {
          responsive: true,
          plugins: {
            legend: { display: false },
            title: { display: true, text: `تحليل ${topic}` }
          },
          scales: {
            y: { beginAtZero: true, max: 100 }
          }
        },
        description: `تحليل البيانات الأساسية المتعلقة بموضوع ${topic}`
      },
      {
        type: 'line',
        title: `اتجاهات ${topic} عبر الزمن`,
        data: {
          labels: ['2020', '2021', '2022', '2023', '2024'],
          datasets: [{
            label: 'التطور',
            data: [40, 55, 70, 80, 95],
            borderColor: '#8B5CF6',
            backgroundColor: 'rgba(139, 92, 246, 0.1)',
            borderWidth: 3,
            fill: true,
            tension: 0.4
          }]
        },
        options: {
          responsive: true,
          plugins: {
            legend: { display: true },
            title: { display: true, text: `تطور ${topic}` }
          },
          scales: {
            y: { beginAtZero: true }
          }
        },
        description: `تطور وتقدم ${topic} خلال السنوات الأخيرة`
      }
    ]
  }

  /**
   * Get statistics for topic
   */
  getTopicStatistics(topic: string): { [key: string]: number | string } {
    const topicLower = topic.toLowerCase()
    
    for (const [key, data] of Object.entries(this.topicData)) {
      if (topicLower.includes(key.toLowerCase()) || 
          (key === 'فلسطين' && (topicLower.includes('palestine') || topicLower.includes('فلسطين'))) ||
          (key === 'تكنولوجيا' && (topicLower.includes('technology') || topicLower.includes('ai'))) ||
          (key === 'تعليم' && (topicLower.includes('education') || topicLower.includes('learning')))) {
        return data.statistics
      }
    }

    // Return generic statistics
    return {
      'معلومة رئيسية': 'قيمة مهمة',
      'إحصائية مفيدة': '85%',
      'رقم بارز': '1.2 مليون',
      'معدل النمو': '12% سنوياً'
    }
  }

  /**
   * Generate chart HTML for embedding in slides with MCP enhancement
   */
  generateChartHTML(chart: ChartData, containerId: string): string {
    const chartConfig = {
      type: chart.type,
      data: chart.data,
      options: {
        ...chart.options,
        responsive: true,
        maintainAspectRatio: false
      }
    }

    return `
      <div class="chart-container" style="position: relative; height: 300px; width: 100%;">
        <canvas id="${containerId}"></canvas>
      </div>
      <script>
        document.addEventListener('DOMContentLoaded', function() {
          const ctx = document.getElementById('${containerId}');
          if (ctx && typeof Chart !== 'undefined') {
            new Chart(ctx, ${JSON.stringify(chartConfig)});
          }
        });
      </script>
    `
  }

  /**
   * Generate enhanced chart using MCP QuickChart service
   */
  async generateMCPChart(chart: ChartData): Promise<MCPResponse> {
    try {
      console.log('📊 Generating MCP-enhanced chart:', chart.title);

      const mcpChartConfig: ChartConfig = {
        type: chart.type as any,
        data: chart.data,
        options: chart.options
      };

      const mcpResponse = await mcpServiceManager.generateChart(mcpChartConfig);

      if (mcpResponse.success) {
        console.log('✅ MCP Chart generated successfully');
        return mcpResponse;
      } else {
        console.warn('⚠️ MCP Chart generation failed, using fallback');
        return this.generateFallbackChart(chart);
      }
    } catch (error) {
      console.error('❌ MCP Chart generation error:', error);
      return this.generateFallbackChart(chart);
    }
  }

  /**
   * Generate fallback chart when MCP service is unavailable
   */
  private generateFallbackChart(chart: ChartData): MCPResponse {
    return {
      success: true,
      data: {
        url: `data:image/svg+xml,${encodeURIComponent(this.generateSVGChart(chart))}`,
        config: chart,
        type: chart.type
      },
      source: 'fallback-svg'
    };
  }

  /**
   * Generate SVG chart as fallback
   */
  private generateSVGChart(chart: ChartData): string {
    const width = 400;
    const height = 300;
    const padding = 40;

    let svg = `<svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
      <rect width="100%" height="100%" fill="#1a1a2e"/>
      <text x="${width/2}" y="30" text-anchor="middle" fill="#ffffff" font-size="16" font-weight="bold">${chart.title}</text>`;

    if (chart.type === 'bar' && chart.data.datasets.length > 0) {
      const dataset = chart.data.datasets[0];
      const maxValue = Math.max(...dataset.data);
      const barWidth = (width - 2 * padding) / chart.data.labels.length;
      const chartHeight = height - 2 * padding - 40;

      chart.data.labels.forEach((label, index) => {
        const value = dataset.data[index];
        const barHeight = (value / maxValue) * chartHeight;
        const x = padding + index * barWidth;
        const y = height - padding - barHeight;

        svg += `
          <rect x="${x + 5}" y="${y}" width="${barWidth - 10}" height="${barHeight}"
                fill="${Array.isArray(dataset.backgroundColor) ? dataset.backgroundColor[index] : dataset.backgroundColor || '#3B82F6'}"
                stroke="#374151" stroke-width="1"/>
          <text x="${x + barWidth/2}" y="${height - 10}" text-anchor="middle" fill="#ffffff" font-size="10">${label}</text>
          <text x="${x + barWidth/2}" y="${y - 5}" text-anchor="middle" fill="#ffffff" font-size="10">${value}</text>
        `;
      });
    }

    svg += '</svg>';
    return svg;
  }

  /**
   * Generate multiple charts for a presentation
   */
  generatePresentationCharts(topic: string): { html: string; charts: ChartData[] } {
    const charts = this.generateChartsForTopic(topic)
    let html = ''
    
    charts.forEach((chart, index) => {
      const containerId = `chart-${index}-${Date.now()}`
      html += this.generateChartHTML(chart, containerId)
    })

    return { html, charts }
  }
}

// Export singleton instance
export const chartGeneratorService = new ChartGeneratorService()

// Export types
export type { ChartData }
