'use client'

import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'

interface ThinkingStep {
  id: string
  title: string
  description: string
  status: 'pending' | 'active' | 'completed'
  details: string[]
  progress: number
}

interface InlineThinkingProcessProps {
  topic: string
  onComplete: (result: string) => void
  isVisible: boolean
  locale?: 'ar' | 'en'
}

const InlineThinkingProcess: React.FC<InlineThinkingProcessProps> = ({
  topic,
  onComplete,
  isVisible,
  locale = 'ar'
}) => {
  const [currentStep, setCurrentStep] = useState(0)
  const [steps, setSteps] = useState<ThinkingStep[]>([])
  const [isProcessing, setIsProcessing] = useState(false)

  // Localized step definitions
  const getLocalizedSteps = (): ThinkingStep[] => {
    if (locale === 'ar') {
      return [
        {
          id: 'analyze',
          title: '🧠 تحليل الموضوع',
          description: 'تحليل الطلب وفهم المتطلبات',
          status: 'pending',
          details: [],
          progress: 0
        },
        {
          id: 'research',
          title: '🔍 البحث والاستكشاف',
          description: 'البحث عن المعلومات والصور ذات الصلة',
          status: 'pending',
          details: [],
          progress: 0
        },
        {
          id: 'structure',
          title: '📋 تخطيط الهيكل',
          description: 'تصميم هيكل العرض التقديمي',
          status: 'pending',
          details: [],
          progress: 0
        },
        {
          id: 'design',
          title: '🎨 اختيار التصميم',
          description: 'اختيار الألوان والتخطيطات المناسبة',
          status: 'pending',
          details: [],
          progress: 0
        },
        {
          id: 'content',
          title: '✍️ إنشاء المحتوى',
          description: 'كتابة المحتوى وإضافة الرسوم البيانية',
          status: 'pending',
          details: [],
          progress: 0
        },
        {
          id: 'finalize',
          title: '✅ المراجعة والإنهاء',
          description: 'مراجعة وتحسين العرض التقديمي',
          status: 'pending',
          details: [],
          progress: 0
        }
      ]
    } else {
      return [
        {
          id: 'analyze',
          title: '🧠 Analyzing Topic',
          description: 'Understanding the request and requirements',
          status: 'pending',
          details: [],
          progress: 0
        },
        {
          id: 'research',
          title: '🔍 Research & Discovery',
          description: 'Finding relevant information and images',
          status: 'pending',
          details: [],
          progress: 0
        },
        {
          id: 'structure',
          title: '📋 Planning Structure',
          description: 'Designing the presentation structure',
          status: 'pending',
          details: [],
          progress: 0
        },
        {
          id: 'design',
          title: '🎨 Choosing Design',
          description: 'Selecting colors and layouts',
          status: 'pending',
          details: [],
          progress: 0
        },
        {
          id: 'content',
          title: '✍️ Creating Content',
          description: 'Writing content and adding charts',
          status: 'pending',
          details: [],
          progress: 0
        },
        {
          id: 'finalize',
          title: '✅ Review & Finalize',
          description: 'Reviewing and improving the presentation',
          status: 'pending',
          details: [],
          progress: 0
        }
      ]
    }
  }

  useEffect(() => {
    if (isVisible && topic) {
      setSteps(getLocalizedSteps())
      startThinkingProcess()
    }
  }, [isVisible, topic, locale])

  const startThinkingProcess = async () => {
    setIsProcessing(true)
    const localizedSteps = getLocalizedSteps()
    setSteps(localizedSteps)

    // Process each step
    for (let i = 0; i < localizedSteps.length; i++) {
      await processStep(i)
    }

    setIsProcessing(false)
    
    // Simulate presentation generation
    setTimeout(() => {
      const mockResult = generateMockPresentation(topic)
      onComplete(mockResult)
    }, 1000)
  }

  const processStep = async (stepIndex: number) => {
    // Mark step as active
    setSteps(prev => prev.map((step, index) => ({
      ...step,
      status: index === stepIndex ? 'active' : index < stepIndex ? 'completed' : 'pending'
    })))
    
    setCurrentStep(stepIndex)

    // Get localized details for this step
    const details = getStepDetails(stepIndex)

    // Simulate processing with progress
    for (let i = 0; i < details.length; i++) {
      await new Promise(resolve => setTimeout(resolve, 800 + Math.random() * 400))
      
      setSteps(prev => prev.map((step, index) => {
        if (index === stepIndex) {
          return {
            ...step,
            details: details.slice(0, i + 1),
            progress: ((i + 1) / details.length) * 100
          }
        }
        return step
      }))
    }

    // Mark step as completed
    await new Promise(resolve => setTimeout(resolve, 300))
    setSteps(prev => prev.map((step, index) => ({
      ...step,
      status: index === stepIndex ? 'completed' : step.status
    })))
  }

  const getStepDetails = (stepIndex: number): string[] => {
    const arabicDetails = [
      [ // Analyze
        `تحليل الموضوع: "${topic}"`,
        'تحديد نوع المحتوى والجمهور المستهدف',
        'فهم السياق والأهداف التعليمية',
        'تحديد مستوى التعقيد المطلوب'
      ],
      [ // Research
        'البحث عن معلومات موثوقة ومحدثة',
        'العثور على صور عالية الجودة ذات صلة',
        'جمع البيانات والإحصائيات المهمة',
        'تحديد المصادر والمراجع المناسبة'
      ],
      [ // Structure
        'تصميم هيكل العرض التقديمي المنطقي',
        'تحديد عدد الشرائح وأنواعها',
        'ترتيب المعلومات بتسلسل مفهوم',
        'اختيار أفضل قوالب الشرائح'
      ],
      [ // Design
        'اختيار مجموعة الألوان المناسبة للموضوع',
        'تحديد الخطوط والتنسيقات البصرية',
        'تصميم التخطيطات والعناصر التفاعلية',
        'إنشاء الرسوم البيانية والمخططات'
      ],
      [ // Content
        'كتابة المحتوى التعليمي والمعلوماتي',
        'إنشاء الشرائح بالتصميمات المختارة',
        'إضافة الصور والعناصر البصرية',
        'تحسين التدفق والانسيابية بين الشرائح'
      ],
      [ // Finalize
        'مراجعة شاملة للمحتوى والتصميم',
        'التأكد من الدقة والجودة العالية',
        'تحسين التجربة التفاعلية والتنقل',
        'إنهاء العرض التقديمي وتجهيزه للعرض'
      ]
    ]

    const englishDetails = [
      [ // Analyze
        `Analyzing topic: "${topic}"`,
        'Identifying content type and target audience',
        'Understanding context and learning objectives',
        'Determining required complexity level'
      ],
      [ // Research
        'Searching for reliable and updated information',
        'Finding high-quality relevant images',
        'Gathering important data and statistics',
        'Identifying appropriate sources and references'
      ],
      [ // Structure
        'Designing logical presentation structure',
        'Determining number and types of slides',
        'Organizing information in understandable sequence',
        'Selecting best slide templates'
      ],
      [ // Design
        'Choosing appropriate color scheme for topic',
        'Selecting fonts and visual formatting',
        'Designing layouts and interactive elements',
        'Creating charts and diagrams'
      ],
      [ // Content
        'Writing educational and informational content',
        'Creating slides with chosen designs',
        'Adding images and visual elements',
        'Improving flow and continuity between slides'
      ],
      [ // Finalize
        'Comprehensive review of content and design',
        'Ensuring accuracy and high quality',
        'Improving interactive experience and navigation',
        'Finalizing presentation and preparing for display'
      ]
    ]

    return locale === 'ar' ? arabicDetails[stepIndex] : englishDetails[stepIndex]
  }

  const generateMockPresentation = (topic: string): string => {
    // This would normally call the advanced AI manager
    // For now, return a simple success message
    return `Presentation for "${topic}" generated successfully!`
  }

  if (!isVisible) return null

  const completedSteps = steps.filter(step => step.status === 'completed').length
  const progressPercentage = (completedSteps / steps.length) * 100

  return (
    <div className="w-full max-w-4xl mx-auto bg-white dark:bg-gray-800 rounded-2xl shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3 rtl:space-x-reverse">
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
              className="text-2xl"
            >
              🤖
            </motion.div>
            <div>
              <h3 className="text-lg font-bold">
                {locale === 'ar' ? 'WIDDX AI يعمل...' : 'WIDDX AI Working...'}
              </h3>
              <p className="text-sm text-blue-100">
                {locale === 'ar' 
                  ? `إنشاء عرض تقديمي عن: ${topic}`
                  : `Creating presentation about: ${topic}`
                }
              </p>
            </div>
          </div>
          <div className="text-right">
            <div className="text-sm font-medium">
              {completedSteps} / {steps.length}
            </div>
            <div className="text-xs text-blue-200">
              {locale === 'ar' ? 'مكتملة' : 'completed'}
            </div>
          </div>
        </div>
        
        {/* Progress Bar */}
        <div className="mt-3 w-full bg-blue-800 rounded-full h-2">
          <motion.div
            className="bg-white h-2 rounded-full"
            initial={{ width: 0 }}
            animate={{ width: `${progressPercentage}%` }}
            transition={{ duration: 0.5 }}
          />
        </div>
      </div>

      {/* Steps */}
      <div className="p-4 space-y-3 max-h-96 overflow-y-auto">
        {steps.map((step, index) => (
          <motion.div
            key={step.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className={`border rounded-lg p-3 transition-all duration-300 ${
              step.status === 'active' 
                ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' 
                : step.status === 'completed'
                ? 'border-green-500 bg-green-50 dark:bg-green-900/20'
                : 'border-gray-200 dark:border-gray-700'
            }`}
          >
            <div className="flex items-center justify-between mb-2">
              <h4 className={`font-semibold text-sm ${
                step.status === 'active' ? 'text-blue-700 dark:text-blue-300' :
                step.status === 'completed' ? 'text-green-700 dark:text-green-300' :
                'text-gray-600 dark:text-gray-400'
              }`}>
                {step.title}
              </h4>
              
              {step.status === 'active' && (
                <div className="animate-spin w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full" />
              )}
              
              {step.status === 'completed' && (
                <div className="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
                  <svg className="w-2.5 h-2.5 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
              )}
            </div>

            <p className="text-xs text-gray-600 dark:text-gray-400 mb-2">
              {step.description}
            </p>

            {/* Progress Bar for Active Step */}
            {step.status === 'active' && step.progress > 0 && (
              <div className="mb-2">
                <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5">
                  <motion.div
                    className="bg-blue-500 h-1.5 rounded-full"
                    initial={{ width: 0 }}
                    animate={{ width: `${step.progress}%` }}
                    transition={{ duration: 0.3 }}
                  />
                </div>
              </div>
            )}

            {/* Details */}
            <AnimatePresence>
              {step.details && step.details.length > 0 && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  className="space-y-1"
                >
                  {step.details.map((detail, detailIndex) => (
                    <motion.div
                      key={detailIndex}
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: detailIndex * 0.05 }}
                      className="text-xs text-gray-700 dark:text-gray-300 flex items-center"
                    >
                      <span className="w-1.5 h-1.5 bg-blue-400 rounded-full mr-2 flex-shrink-0" />
                      {detail}
                    </motion.div>
                  ))}
                </motion.div>
              )}
            </AnimatePresence>
          </motion.div>
        ))}
      </div>

      {/* Footer */}
      <div className="bg-gray-50 dark:bg-gray-900 p-3 text-center">
        <p className="text-xs text-gray-600 dark:text-gray-400">
          {isProcessing 
            ? (locale === 'ar' 
                ? 'جاري العمل على إنشاء عرض تقديمي استثنائي...' 
                : 'Working on creating an exceptional presentation...'
              )
            : (locale === 'ar' ? 'تم الانتهاء!' : 'Completed!')
          }
        </p>
      </div>
    </div>
  )
}

export default InlineThinkingProcess
