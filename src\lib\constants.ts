// Application Constants
export const APP_CONFIG = {
  // Timeouts (in milliseconds)
  API_TIMEOUT: 30000, // 30 seconds
  SPLASH_SCREEN_DURATION: 2500, // 2.5 seconds
  
  // Rate limiting
  RATE_LIMIT_WINDOW: 60 * 1000, // 1 minute
  
  // UI Constants
  MAX_MESSAGE_LENGTH: 4000,
  TEXTAREA_MAX_HEIGHT: 128, // pixels
  
  // Default values
  DEFAULT_TEMPERATURE: 0.7,
  DEFAULT_MAX_TOKENS: 4000,
  DEFAULT_TOP_P: 0.95,
  DEFAULT_FREQUENCY_PENALTY: 0,
  DEFAULT_PRESENCE_PENALTY: 0,
  
  // API Configuration
  DEFAULT_DEEPSEEK_URL: 'https://api.deepseek.com/v1',
  DEFAULT_GEMINI_URL: 'https://generativelanguage.googleapis.com/v1beta',
  
  // Gemini Safety Settings
  GEMINI_SAFETY_THRESHOLD: 'BLOCK_MEDIUM_AND_ABOVE',
  GEMINI_SAFETY_CATEGORIES: [
    'HARM_CATEGORY_HARASSMENT',
    'HARM_CATEGORY_HATE_SPEECH',
    'HARM_CATEGORY_SEXUALLY_EXPLICIT',
    'HARM_CATEGORY_DANGEROUS_CONTENT'
  ],
  
  // Chart rendering
  CHART_RENDER_TIMEOUT: 3000, // 3 seconds
  CHART_ANIMATION_DELAY: 1000, // 1 second
  
  // Storage keys
  STORAGE_KEYS: {
    THEME: 'widdx-ai-theme',
    LOCALE: 'widdx-ai-locale',
    CHATS: 'widdx-ai-chats',
    CURRENT_CHAT: 'widdx-ai-current-chat'
  },
  
  // External CDN URLs
  CDN_URLS: {
    JSPDF: 'https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.2/jspdf.umd.min.js',
    HTML2PDF: 'https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.3/html2pdf.bundle.min.js',
    CHART_JS: 'https://cdnjs.cloudflare.com/ajax/libs/Chart.js/4.5.0/chart.umd.js'
  },
  
  // Font URLs
  FONTS: {
    GOOGLE_FONTS_PRECONNECT: 'https://fonts.googleapis.com',
    GOOGLE_FONTS_STATIC: 'https://fonts.gstatic.com',
    TAJAWAL_URL: 'https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800;900&display=swap'
  }
} as const

// Theme colors (for reference, actual colors are in CSS variables)
export const THEME_COLORS = {
  LIGHT: {
    PRIMARY: '#F4F6F8',
    SECONDARY: '#ffffff',
    ACCENT: '#3b82f6',
    TEXT_PRIMARY: '#1a1a1a'
  },
  DARK: {
    PRIMARY: '#141618',
    SECONDARY: '#1f2937',
    ACCENT: '#60a5fa',
    TEXT_PRIMARY: '#f9fafb'
  }
} as const

// Supported locales
export const SUPPORTED_LOCALES = ['en', 'ar'] as const

// Chat modes
export const CHAT_MODES = [
  'chat',
  'slides',
  'code',
  'summarizer',
  'writer',
  'research'
] as const

// AI Models configuration
export const AI_MODEL_DEFAULTS = {
  DEEPSEEK: {
    MODEL: 'deepseek-chat',
    TOP_K: 40
  },
  GEMINI: {
    MODEL: 'gemini-1.5-flash',
    TOP_K: 40
  }
} as const
