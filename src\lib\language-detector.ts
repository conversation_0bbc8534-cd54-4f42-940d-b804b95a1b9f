import type { Locale } from '@/types'

/**
 * Detects the language of a given text
 * Returns 'ar' for Arabic text, 'en' for English text
 */
export function detectLanguage(text: string): Locale {
  if (!text || text.trim().length === 0) {
    return 'en' // Default to English for empty text
  }

  // Remove punctuation, numbers, and whitespace for analysis
  const cleanText = text.replace(/[^\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFFa-zA-Z]/g, '')
  
  if (cleanText.length === 0) {
    return 'en' // Default to English if no letters found
  }

  // Count Arabic characters
  const arabicChars = cleanText.match(/[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]/g)
  const arabicCount = arabicChars ? arabicChars.length : 0
  
  // Count English characters
  const englishChars = cleanText.match(/[a-zA-Z]/g)
  const englishCount = englishChars ? englishChars.length : 0
  
  // Calculate percentages
  const totalChars = arabicCount + englishCount
  if (totalChars === 0) {
    return 'en' // Default to English if no recognizable characters
  }
  
  const arabicPercentage = arabicCount / totalChars
  
  // If more than 30% of characters are Arabic, consider it Arabic text
  return arabicPercentage > 0.3 ? 'ar' : 'en'
}

/**
 * Detects the primary language from an array of messages
 * Uses the last user message for detection, with fallback to previous messages
 */
export function detectLanguageFromMessages(messages: Array<{ role: string; content: string }>): Locale {
  if (!messages || messages.length === 0) {
    return 'en'
  }

  // Find the last user message
  const userMessages = messages.filter(msg => msg.role === 'user')
  if (userMessages.length === 0) {
    return 'en'
  }

  // Use the last user message for language detection
  const lastUserMessage = userMessages[userMessages.length - 1]
  return detectLanguage(lastUserMessage.content)
}

/**
 * Common Arabic words for additional context
 */
const ARABIC_WORDS = [
  'في', 'من', 'إلى', 'على', 'هذا', 'هذه', 'التي', 'الذي', 'كان', 'كانت',
  'يكون', 'تكون', 'أن', 'أو', 'لا', 'نعم', 'كيف', 'ماذا', 'متى', 'أين',
  'لماذا', 'مع', 'بعد', 'قبل', 'عند', 'حول', 'خلال', 'بين', 'تحت', 'فوق'
]

/**
 * Common English words for additional context
 */
const ENGLISH_WORDS = [
  'the', 'be', 'to', 'of', 'and', 'a', 'in', 'that', 'have', 'i',
  'it', 'for', 'not', 'on', 'with', 'he', 'as', 'you', 'do', 'at',
  'this', 'but', 'his', 'by', 'from', 'they', 'we', 'say', 'her', 'she'
]

/**
 * Enhanced language detection using word analysis
 */
export function detectLanguageEnhanced(text: string): Locale {
  const basicDetection = detectLanguage(text)
  
  // For short texts, use word-based detection as additional validation
  if (text.trim().split(/\s+/).length <= 10) {
    const words = text.toLowerCase().split(/\s+/)
    
    let arabicWordCount = 0
    let englishWordCount = 0
    
    words.forEach(word => {
      const cleanWord = word.replace(/[^\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFFa-zA-Z]/g, '')
      
      if (ARABIC_WORDS.includes(cleanWord)) {
        arabicWordCount++
      } else if (ENGLISH_WORDS.includes(cleanWord)) {
        englishWordCount++
      }
    })
    
    // If we found language-specific words, use that as additional evidence
    if (arabicWordCount > englishWordCount && arabicWordCount > 0) {
      return 'ar'
    } else if (englishWordCount > arabicWordCount && englishWordCount > 0) {
      return 'en'
    }
  }
  
  return basicDetection
}
