import type { Message, ChatMode, Locale, ApiResponse } from '@/types'
import type { AIResponse } from './ai-manager'

export interface ChatRequest {
  messages: Message[]
  mode: ChatMode
  locale: Locale
  userId?: string
  stream?: boolean
}

export interface StreamChunk {
  type: 'metadata' | 'content' | 'done' | 'error'
  content?: string
  model?: string
  provider?: string
  error?: string
}

export class APIClient {
  private baseUrl: string

  constructor() {
    // Use relative URLs to automatically work with any port
    this.baseUrl = ''
  }

  async sendMessage(request: ChatRequest): Promise<AIResponse> {
    try {
      // Create AbortController for timeout
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), 30000) // 30 second timeout

      const response = await fetch(`${this.baseUrl}/api/chat`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
        signal: controller.signal,
      })

      clearTimeout(timeoutId)

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.error || `HTTP ${response.status}`)
      }

      const data: ApiResponse<AIResponse> = await response.json()

      if (!data.success) {
        throw new Error(data.error || 'API request failed')
      }

      return data.data!
    } catch (error) {
      console.error('API Client Error:', error)

      // Handle timeout and network errors
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          throw new Error('Request timeout - please try again')
        }
        if (error.message.includes('Failed to fetch')) {
          throw new Error('Network error - please check your connection')
        }
      }

      throw error
    }
  }

  async sendMessageStream(
    request: ChatRequest,
    onChunk: (chunk: StreamChunk) => void,
    onComplete: () => void,
    onError: (error: string) => void
  ): Promise<void> {
    try {
      // Create AbortController for timeout
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), 30000) // 30 second timeout

      const response = await fetch(`${this.baseUrl}/api/chat/stream`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(request),
        signal: controller.signal,
      })

      clearTimeout(timeoutId)

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.error || `HTTP ${response.status}`)
      }

      const reader = response.body?.getReader()
      if (!reader) {
        throw new Error('No response body available')
      }

      const decoder = new TextDecoder()
      let buffer = ''

      while (true) {
        const { done, value } = await reader.read()
        
        if (done) {
          onComplete()
          break
        }

        buffer += decoder.decode(value, { stream: true })
        const lines = buffer.split('\n')
        
        // Keep the last incomplete line in the buffer
        buffer = lines.pop() || ''

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = line.slice(6).trim()
              if (data) {
                const chunk: StreamChunk = JSON.parse(data)
                onChunk(chunk)
                
                if (chunk.type === 'error') {
                  onError(chunk.error || 'Stream error')
                  return
                }
                
                if (chunk.type === 'done') {
                  onComplete()
                  return
                }
              }
            } catch (e) {
              console.warn('Failed to parse SSE data:', line)
            }
          }
        }
      }
    } catch (error) {
      console.error('Stream API Client Error:', error)

      // Handle timeout and network errors
      let errorMessage = 'Stream failed'
      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          errorMessage = 'Request timeout - please try again'
        } else if (error.message.includes('Failed to fetch')) {
          errorMessage = 'Network error - please check your connection'
        } else {
          errorMessage = error.message
        }
      }

      onError(errorMessage)
    }
  }

  async checkHealth(): Promise<{
    healthy: boolean
    services: {
      deepseek: { status: string; configured: boolean }
      gemini: { status: string; configured: boolean }
    }
  }> {
    try {
      const response = await fetch(`${this.baseUrl}/api/health`)
      const data = await response.json()
      return data
    } catch (error) {
      console.error('Health check failed:', error)
      return {
        healthy: false,
        services: {
          deepseek: { status: 'unknown', configured: false },
          gemini: { status: 'unknown', configured: false }
        }
      }
    }
  }
}

// Singleton instance
export const apiClient = new APIClient()
