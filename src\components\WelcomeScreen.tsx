'use client'

import { motion } from 'framer-motion'
import { 
  Code, 
  Presentation, 
  PenTool, 
  Search,
  Sparkles,
  ArrowRight
} from 'lucide-react'
import { t } from '@/locales'
import type { WelcomeScreenProps, ChatMode } from '@/types'

const features = [
  {
    icon: Code,
    key: 'code',
    gradient: 'from-blue-500 to-cyan-500',
  },
  {
    icon: Presentation,
    key: 'slides',
    gradient: 'from-purple-500 to-pink-500',
  },
  {
    icon: PenTool,
    key: 'writing',
    gradient: 'from-green-500 to-emerald-500',
  },
  {
    icon: Search,
    key: 'research',
    gradient: 'from-orange-500 to-red-500',
  },
]

const modeButtons: { id: ChatMode; icon: any; gradient: string }[] = [
  { id: 'chat', icon: Sparkles, gradient: 'from-blue-500 to-purple-500' },
  { id: 'slides', icon: Presentation, gradient: 'from-purple-500 to-pink-500' },
  { id: 'code', icon: Code, gradient: 'from-blue-500 to-cyan-500' },
  { id: 'summarizer', icon: Search, gradient: 'from-green-500 to-emerald-500' },
  { id: 'writer', icon: PenTool, gradient: 'from-orange-500 to-red-500' },
  { id: 'research', icon: Search, gradient: 'from-red-500 to-pink-500' },
]

export function WelcomeScreen({
  onModeSelect,
  onPromptSelect,
  currentMode,
  locale,
}: WelcomeScreenProps) {
  const suggestions = [
    'Create a landing page for a tech startup',
    'Help me debug this JavaScript code',
    'Write a business proposal for a new app',
    'Explain quantum computing in simple terms',
    'Generate a presentation about AI trends',
    'Summarize this research paper',
  ]

  const suggestionsAr = [
    'أنشئ صفحة هبوط لشركة تقنية ناشئة',
    'ساعدني في إصلاح هذا الكود البرمجي',
    'اكتب اقتراح عمل لتطبيق جديد',
    'اشرح الحوسبة الك��ية بطريقة بسيطة',
    'أنشئ عرض تقديمي عن اتجاهات الذكاء الاصطناعي',
    'لخص هذا البحث العلمي',
  ]

  const currentSuggestions = locale === 'ar' ? suggestionsAr : suggestions

  return (
    <div className="flex-1 overflow-y-auto">
      <div className="max-w-4xl mx-auto px-4 py-12">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-12"
        >
          {/* Logo */}
          <motion.div
            initial={{ scale: 0.8 }}
            animate={{ scale: 1 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="mb-8"
          >
            <div className="w-20 h-20 mx-auto relative">
              <div className="absolute inset-0 bg-gradient-to-br from-light-accent to-light-accent-hover dark:from-dark-accent dark:to-dark-accent-hover rounded-2xl opacity-20"></div>
              <div className="absolute inset-2 bg-gradient-to-br from-light-accent to-light-accent-hover dark:from-dark-accent dark:to-dark-accent-hover rounded-xl flex items-center justify-center">
                <svg
                  viewBox="0 0 100 100"
                  className="w-10 h-10 text-white"
                  fill="currentColor"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <circle cx="35" cy="40" r="3" />
                  <circle cx="65" cy="40" r="3" />
                  <path
                    d="M30 60 Q50 75 70 60"
                    stroke="currentColor"
                    strokeWidth="2"
                    fill="none"
                  />
                  <text
                    x="50"
                    y="25"
                    textAnchor="middle"
                    fontSize="10"
                    fontWeight="bold"
                    className="fill-current"
                  >
                    WIDDX
                  </text>
                </svg>
              </div>
            </div>
          </motion.div>

          {/* Title */}
          <motion.h1
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            className="text-4xl md:text-5xl font-bold text-light-text-primary dark:text-dark-text-primary mb-4"
          >
            {t(locale, 'welcome.title')}
          </motion.h1>

          {/* Subtitle */}
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="text-xl text-light-text-secondary dark:text-dark-text-secondary mb-12"
          >
            {t(locale, 'welcome.subtitle')}
          </motion.p>
        </motion.div>

        {/* Features Grid */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.5 }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12"
        >
          {features.map((feature, index) => {
            const Icon = feature.icon
            return (
              <motion.div
                key={feature.key}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.6 + index * 0.1 }}
                className="group cursor-pointer"
                onClick={() => {
                  const modeMap: Record<string, ChatMode> = {
                    code: 'code',
                    slides: 'slides',
                    writing: 'writer',
                    research: 'research',
                  }
                  onModeSelect(modeMap[feature.key] || 'chat')
                }}
              >
                <div className="bg-light-secondary dark:bg-dark-secondary border border-light-border dark:border-dark-border rounded-2xl p-6 transition-all duration-300 group-hover:shadow-lg group-hover:scale-105 group-hover:border-light-accent dark:group-hover:border-dark-accent">
                  <div className={`w-12 h-12 rounded-xl bg-gradient-to-br ${feature.gradient} flex items-center justify-center mb-4`}>
                    <Icon className="w-6 h-6 text-white" />
                  </div>
                  <h3 className="text-lg font-semibold text-light-text-primary dark:text-dark-text-primary mb-2">
                    {t(locale, `welcome.features.${feature.key}.title`)}
                  </h3>
                  <p className="text-sm text-light-text-secondary dark:text-dark-text-secondary">
                    {t(locale, `welcome.features.${feature.key}.description`)}
                  </p>
                </div>
              </motion.div>
            )
          })}
        </motion.div>

        {/* Mode Selection */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.8 }}
          className="mb-12"
        >
          <h2 className="text-2xl font-bold text-light-text-primary dark:text-dark-text-primary text-center mb-6">
            Choose Your Mode
          </h2>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-3">
            {modeButtons.map((mode, index) => {
              const Icon = mode.icon
              const isActive = currentMode === mode.id
              return (
                <motion.button
                  key={mode.id}
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.3, delay: 0.9 + index * 0.05 }}
                  onClick={() => onModeSelect(mode.id)}
                  className={`group relative p-4 rounded-xl transition-all duration-300 ${
                    isActive
                      ? 'bg-light-accent dark:bg-dark-accent text-white shadow-lg scale-105'
                      : 'bg-light-secondary dark:bg-dark-secondary border border-light-border dark:border-dark-border hover:border-light-accent dark:hover:border-dark-accent hover:shadow-md hover:scale-105'
                  }`}
                >
                  <div className={`w-8 h-8 mx-auto mb-2 rounded-lg flex items-center justify-center ${
                    isActive
                      ? 'bg-white/20'
                      : `bg-gradient-to-br ${mode.gradient} text-white`
                  }`}>
                    <Icon className="w-4 h-4" />
                  </div>
                  <div className={`text-xs font-medium ${
                    isActive
                      ? 'text-white'
                      : 'text-light-text-primary dark:text-dark-text-primary'
                  }`}>
                    {t(locale, `modes.${mode.id}.name`)}
                  </div>
                </motion.button>
              )
            })}
          </div>
        </motion.div>

        {/* Prompt Suggestions */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 1.0 }}
          className="text-center"
        >
          <h3 className="text-xl font-semibold text-light-text-primary dark:text-dark-text-primary mb-6">
            {t(locale, 'welcome.tryAsking')}
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3 max-w-3xl mx-auto">
            {suggestions.map((suggestion, index) => (
              <motion.button
                key={index}
                initial={{ opacity: 0, x: index % 2 === 0 ? -20 : 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5, delay: 1.1 + index * 0.1 }}
                onClick={() => onPromptSelect(suggestion)}
                className="group flex items-center justify-between p-4 bg-light-secondary dark:bg-dark-secondary border border-light-border dark:border-dark-border rounded-xl hover:border-light-accent dark:hover:border-dark-accent hover:shadow-md transition-all duration-300 text-left"
              >
                <span className="text-sm text-light-text-primary dark:text-dark-text-primary group-hover:text-light-accent dark:group-hover:text-dark-accent transition-colors">
                  {suggestion}
                </span>
                <ArrowRight className="w-4 h-4 text-light-text-muted dark:text-dark-text-muted group-hover:text-light-accent dark:group-hover:text-dark-accent transition-colors opacity-0 group-hover:opacity-100 transform translate-x-2 group-hover:translate-x-0" />
              </motion.button>
            ))}
          </div>
        </motion.div>
      </div>
    </div>
  )
}