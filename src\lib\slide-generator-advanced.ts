import { SlideTemplate, getTemplate } from './slide-templates'

export interface PresentationData {
  title: string
  subtitle?: string
  author?: string
  institution?: string
  date?: string
  slides: SlideData[]
  template: string
  language?: 'ar' | 'en'
}

export interface SlideData {
  type: 'title' | 'content' | 'chart' | 'comparison' | 'conclusion'
  title: string
  content?: string
  chartData?: ChartData
  comparisonData?: ComparisonData
}

export interface ChartData {
  type: 'doughnut' | 'radar' | 'bar' | 'line'
  labels: string[]
  datasets: ChartDataset[]
  options?: any
}

export interface ChartDataset {
  label: string
  data: number[]
  backgroundColor?: string[]
  borderColor?: string[]
  borderWidth?: number
}

export interface ComparisonData {
  items: ComparisonItem[]
}

export interface ComparisonItem {
  title: string
  features: string[]
  price?: string
  badge?: string
  icon?: string
}

export class AdvancedSlideGenerator {
  private template: SlideTemplate

  constructor(templateId: string = 'arabic-professional') {
    const template = getTemplate(templateId)
    if (!template) {
      throw new Error(`Template ${templateId} not found`)
    }
    this.template = template
  }

  generatePresentation(data: PresentationData): string {
    const slides = data.slides.map((slide, index) => {
      return this.generateSlide(slide, index + 1, data)
    }).join('\n')

    return this.wrapInDocument(slides, data)
  }

  private generateSlide(slide: SlideData, slideNumber: number, presentationData: PresentationData): string {
    switch (slide.type) {
      case 'title':
        return this.generateTitleSlide(slide, presentationData)
      case 'content':
        return this.generateContentSlide(slide, slideNumber)
      case 'chart':
        return this.generateChartSlide(slide, slideNumber)
      case 'comparison':
        return this.generateComparisonSlide(slide, slideNumber)
      case 'conclusion':
        return this.generateConclusionSlide(slide, slideNumber)
      default:
        return this.generateContentSlide(slide, slideNumber)
    }
  }

  private generateTitleSlide(slide: SlideData, data: PresentationData): string {
    return this.template.structure.titleSlide
      .replace(/\{\{title\}\}/g, slide.title)
      .replace(/\{\{subtitle\}\}/g, data.subtitle || '')
      .replace(/\{\{author\}\}/g, data.author || 'WIDDX AI')
      .replace(/\{\{institution\}\}/g, data.institution || '')
      .replace(/\{\{date\}\}/g, data.date || new Date().toLocaleDateString('ar-SA'))
  }

  private generateContentSlide(slide: SlideData, slideNumber: number): string {
    const content = this.formatContent(slide.content || '')
    
    return this.template.structure.contentSlide
      .replace(/\{\{title\}\}/g, slide.title)
      .replace(/\{\{content\}\}/g, content)
      .replace(/\{\{slideNumber\}\}/g, slideNumber.toString())
      .replace(/\{\{leftContent\}\}/g, content)
      .replace(/\{\{rightContent\}\}/g, '')
  }

  private generateChartSlide(slide: SlideData, slideNumber: number): string {
    if (!slide.chartData) {
      return this.generateContentSlide(slide, slideNumber)
    }

    const chartId = `chart_${slideNumber}_${Date.now()}`
    const chartScript = this.generateChartScript(chartId, slide.chartData)

    return this.template.structure.chartSlide
      .replace(/\{\{title\}\}/g, slide.title)
      .replace(/\{\{chartTitle\}\}/g, slide.title)
      .replace(/\{\{chartId\}\}/g, chartId)
      .replace(/\{\{slideNumber\}\}/g, slideNumber.toString()) + 
      `\n<script>${chartScript}</script>`
  }

  private generateComparisonSlide(slide: SlideData, slideNumber: number): string {
    if (!slide.comparisonData) {
      return this.generateContentSlide(slide, slideNumber)
    }

    const comparisonContent = this.generateComparisonContent(slide.comparisonData)

    return this.template.structure.comparisonSlide
      .replace(/\{\{title\}\}/g, slide.title)
      .replace(/\{\{comparisonContent\}\}/g, comparisonContent)
      .replace(/\{\{slideNumber\}\}/g, slideNumber.toString())
  }

  private generateConclusionSlide(slide: SlideData, slideNumber: number): string {
    const conclusionPoints = this.formatConclusionPoints(slide.content || '')

    return this.template.structure.conclusionSlide
      .replace(/\{\{title\}\}/g, slide.title)
      .replace(/\{\{conclusionPoints\}\}/g, conclusionPoints)
      .replace(/\{\{slideNumber\}\}/g, slideNumber.toString())
  }

  private formatContent(content: string): string {
    // Convert markdown-like content to HTML
    const lines = content.split('\n')
    let html = ''

    for (const line of lines) {
      const trimmed = line.trim()
      if (trimmed.startsWith('- ')) {
        if (!html.includes('<ul>')) {
          html += '<ul class="feature-list">'
        }
        html += `<li class="feature-item"><i class="fas fa-check-circle feature-icon"></i><span>${trimmed.substring(2)}</span></li>`
      } else if (trimmed.startsWith('# ')) {
        if (html.includes('<ul>') && !html.includes('</ul>')) {
          html += '</ul>'
        }
        html += `<h3>${trimmed.substring(2)}</h3>`
      } else if (trimmed) {
        if (html.includes('<ul>') && !html.includes('</ul>')) {
          html += '</ul>'
        }
        html += `<p>${trimmed}</p>`
      }
    }

    if (html.includes('<ul>') && !html.includes('</ul>')) {
      html += '</ul>'
    }

    return html
  }

  private formatConclusionPoints(content: string): string {
    const lines = content.split('\n').filter(line => line.trim())
    return lines.map(line => {
      const trimmed = line.trim().replace(/^- /, '')
      return `<div class="feature-item"><i class="fas fa-check-circle feature-icon"></i><span>${trimmed}</span></div>`
    }).join('')
  }

  private generateComparisonContent(data: ComparisonData): string {
    return data.items.map(item => `
      <div class="card">
        ${item.badge ? `<div class="price-tag">${item.badge}</div>` : ''}
        <div class="card-header">
          <div class="card-icon">
            <i class="fas ${item.icon || 'fa-star'}"></i>
          </div>
          <h2 class="card-title">${item.title}</h2>
        </div>
        <div class="feature-list">
          ${item.features.map(feature => `
            <div class="feature-item">
              <i class="fas fa-check-circle feature-icon"></i>
              <span>${feature}</span>
            </div>
          `).join('')}
        </div>
        ${item.price ? `<div class="highlight" style="margin-top: 15px; font-size: 20px;">${item.price}</div>` : ''}
      </div>
    `).join('')
  }

  private generateChartScript(chartId: string, chartData: ChartData): string {
    return `
      document.addEventListener('DOMContentLoaded', function() {
        const ctx = document.getElementById('${chartId}').getContext('2d');
        
        const chart = new Chart(ctx, {
          type: '${chartData.type}',
          data: ${JSON.stringify({
            labels: chartData.labels,
            datasets: chartData.datasets
          })},
          options: ${JSON.stringify(chartData.options || this.getDefaultChartOptions(chartData.type))}
        });
      });
    `
  }

  private getDefaultChartOptions(type: string): any {
    const baseOptions = {
      maintainAspectRatio: false,
      plugins: {
        legend: {
          labels: {
            color: 'rgba(255, 255, 255, 0.8)',
            font: {
              family: this.template.language === 'ar' ? 'Tajawal' : 'Inter',
              size: 14
            }
          }
        }
      }
    }

    switch (type) {
      case 'doughnut':
        return {
          ...baseOptions,
          cutout: '60%'
        }
      case 'radar':
        return {
          ...baseOptions,
          scales: {
            r: {
              angleLines: { color: 'rgba(255, 255, 255, 0.1)' },
              grid: { color: 'rgba(255, 255, 255, 0.1)' },
              pointLabels: {
                color: 'rgba(255, 255, 255, 0.7)',
                font: { family: this.template.language === 'ar' ? 'Tajawal' : 'Inter' }
              },
              ticks: {
                color: 'rgba(255, 255, 255, 0.5)',
                backdropColor: 'transparent'
              }
            }
          }
        }
      default:
        return baseOptions
    }
  }

  private wrapInDocument(slides: string, data: PresentationData): string {
    const dependencies = this.generateDependencies()

    return `<!DOCTYPE html>
<html lang="${this.template.language}" dir="${this.template.direction}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <title>${data.title}</title>
    ${dependencies}
    <style>
      /* Base responsive styles */
      html, body {
        margin: 0;
        padding: 0;
        width: 100%;
        height: 100%;
        overflow-x: hidden;
        -webkit-text-size-adjust: 100%;
        -ms-text-size-adjust: 100%;
      }

      /* Prevent zoom on input focus on iOS */
      input, textarea, select {
        font-size: 16px;
      }

      ${this.template.css}
    </style>
</head>
<body>
    ${slides}
    ${this.generateNavigationScript()}
</body>
</html>`
  }

  private generateDependencies(): string {
    let deps = ''
    
    if (this.template.fonts.includes('Tajawal')) {
      deps += '<link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;900&display=swap" rel="stylesheet">\n'
    }
    
    if (this.template.fonts.includes('Inter')) {
      deps += '<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">\n'
    }
    
    if (this.template.dependencies.includes('fontawesome')) {
      deps += '<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css">\n'
    }
    
    if (this.template.dependencies.includes('chart.js')) {
      deps += '<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>\n'
    }
    
    return deps
  }

  private generateNavigationScript(): string {
    return `
<script>
// Advanced slide presentation navigation
let currentSlideIndex = 0;
let slides = [];
let totalSlides = 0;

document.addEventListener('DOMContentLoaded', function() {
  // Initialize slide navigation
  slides = document.querySelectorAll('.slide');
  totalSlides = slides.length;

  if (slides.length > 0) {
    // Hide all slides except the first one
    slides.forEach((slide, index) => {
      if (index > 0) {
        slide.style.display = 'none';
      }
    });

    // Create navigation controls
    createNavigationControls();

    // Add keyboard navigation
    document.addEventListener('keydown', handleKeyNavigation);

    // Add touch/swipe navigation for mobile
    addTouchNavigation();
  }
});

function createNavigationControls() {
  // Create navigation container
  const navContainer = document.createElement('div');
  navContainer.className = 'slide-navigation';
  navContainer.style.cssText = \`
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1000;
    display: flex;
    align-items: center;
    gap: 15px;
    background: rgba(0, 0, 0, 0.8);
    padding: 12px 20px;
    border-radius: 30px;
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  \`;

  // Previous button
  const prevBtn = document.createElement('button');
  prevBtn.innerHTML = '‹';
  prevBtn.className = 'nav-btn prev-btn';
  prevBtn.onclick = previousSlide;
  prevBtn.style.cssText = \`
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    font-weight: bold;
    cursor: pointer;
    padding: 8px 12px;
    border-radius: 50%;
    transition: all 0.3s ease;
    opacity: 0.7;
  \`;

  // Slide counter
  const slideCounter = document.createElement('div');
  slideCounter.className = 'slide-counter';
  slideCounter.style.cssText = \`
    color: white;
    font-size: 14px;
    font-weight: 500;
    min-width: 60px;
    text-align: center;
  \`;
  updateSlideCounter();

  // Next button
  const nextBtn = document.createElement('button');
  nextBtn.innerHTML = '›';
  nextBtn.className = 'nav-btn next-btn';
  nextBtn.onclick = nextSlide;
  nextBtn.style.cssText = \`
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    font-weight: bold;
    cursor: pointer;
    padding: 8px 12px;
    border-radius: 50%;
    transition: all 0.3s ease;
    opacity: 0.7;
  \`;

  // Fullscreen button
  const fullscreenBtn = document.createElement('button');
  fullscreenBtn.innerHTML = '⛶';
  fullscreenBtn.className = 'nav-btn fullscreen-btn';
  fullscreenBtn.onclick = toggleFullscreen;
  fullscreenBtn.style.cssText = \`
    background: none;
    border: none;
    color: white;
    font-size: 16px;
    cursor: pointer;
    padding: 8px 12px;
    border-radius: 50%;
    transition: all 0.3s ease;
    opacity: 0.7;
  \`;

  // Add hover effects
  [prevBtn, nextBtn, fullscreenBtn].forEach(btn => {
    btn.addEventListener('mouseenter', () => {
      btn.style.opacity = '1';
      btn.style.background = 'rgba(255, 255, 255, 0.2)';
    });
    btn.addEventListener('mouseleave', () => {
      btn.style.opacity = '0.7';
      btn.style.background = 'none';
    });
  });

  navContainer.appendChild(prevBtn);
  navContainer.appendChild(slideCounter);
  navContainer.appendChild(nextBtn);
  navContainer.appendChild(fullscreenBtn);

  document.body.appendChild(navContainer);
}

function showSlide(index) {
  if (index < 0 || index >= totalSlides) return;

  // Hide all slides
  slides.forEach((slide, i) => {
    slide.style.display = 'none';
  });

  // Show current slide
  slides[index].style.display = 'flex';
  currentSlideIndex = index;

  updateSlideCounter();
  updateNavigationButtons();
}

function updateSlideCounter() {
  const counter = document.querySelector('.slide-counter');
  if (counter) {
    counter.textContent = \`\${currentSlideIndex + 1} / \${totalSlides}\`;
  }
}

function updateNavigationButtons() {
  const prevBtn = document.querySelector('.prev-btn');
  const nextBtn = document.querySelector('.next-btn');

  if (prevBtn) {
    prevBtn.style.opacity = currentSlideIndex === 0 ? '0.3' : '0.7';
    prevBtn.style.cursor = currentSlideIndex === 0 ? 'not-allowed' : 'pointer';
  }

  if (nextBtn) {
    nextBtn.style.opacity = currentSlideIndex === totalSlides - 1 ? '0.3' : '0.7';
    nextBtn.style.cursor = currentSlideIndex === totalSlides - 1 ? 'not-allowed' : 'pointer';
  }
}

function nextSlide() {
  if (currentSlideIndex < totalSlides - 1) {
    showSlide(currentSlideIndex + 1);
  }
}

function previousSlide() {
  if (currentSlideIndex > 0) {
    showSlide(currentSlideIndex - 1);
  }
}

function toggleFullscreen() {
  if (!document.fullscreenElement) {
    document.documentElement.requestFullscreen().catch(err => {
      console.log('Error attempting to enable fullscreen:', err);
    });
  } else {
    document.exitFullscreen();
  }
}

function handleKeyNavigation(e) {
  switch(e.key) {
    case 'ArrowRight':
    case ' ':
      e.preventDefault();
      nextSlide();
      break;
    case 'ArrowLeft':
      e.preventDefault();
      previousSlide();
      break;
    case 'Home':
      e.preventDefault();
      showSlide(0);
      break;
    case 'End':
      e.preventDefault();
      showSlide(totalSlides - 1);
      break;
    case 'f':
    case 'F11':
      e.preventDefault();
      toggleFullscreen();
      break;
    case 'Escape':
      if (document.fullscreenElement) {
        document.exitFullscreen();
      }
      break;
  }
}

function addTouchNavigation() {
  let startX = 0;
  let startY = 0;

  document.addEventListener('touchstart', (e) => {
    startX = e.touches[0].clientX;
    startY = e.touches[0].clientY;
  });

  document.addEventListener('touchend', (e) => {
    if (!startX || !startY) return;

    const endX = e.changedTouches[0].clientX;
    const endY = e.changedTouches[0].clientY;

    const diffX = startX - endX;
    const diffY = startY - endY;

    // Only handle horizontal swipes
    if (Math.abs(diffX) > Math.abs(diffY) && Math.abs(diffX) > 50) {
      if (diffX > 0) {
        // Swipe left - next slide
        nextSlide();
      } else {
        // Swipe right - previous slide
        previousSlide();
      }
    }

    startX = 0;
    startY = 0;
  });
}
</script>`
  }

  // Static method for fallback generation
  static generateFallbackPresentation(topic: string, templateId: string = 'arabic-professional'): string {
    const generator = new AdvancedSlideGenerator(templateId)
    
    const presentationData: PresentationData = {
      title: topic,
      subtitle: 'عرض تقديمي تم إنشاؤه بواسطة WIDDX AI',
      author: 'WIDDX AI',
      date: new Date().toLocaleDateString('ar-SA'),
      template: templateId,
      language: templateId === 'arabic-professional' ? 'ar' : 'en',
      slides: generator.generateFallbackSlides(topic)
    }

    return generator.generatePresentation(presentationData)
  }

  private generateFallbackSlides(topic: string): SlideData[] {
    const lowerTopic = topic.toLowerCase()
    const arabicTopic = topic

    // Palestine/فلسطين specific content
    if (lowerTopic.includes('palestine') || lowerTopic.includes('فلسطين') || arabicTopic.includes('فلسطين')) {
      return this.generatePalestineResearchSlides()
    }

    // AI/Technology topics
    if (lowerTopic.includes('artificial intelligence') || lowerTopic.includes('ai') ||
        lowerTopic.includes('ذكاء اصطناعي') || lowerTopic.includes('تعلم آلي')) {
      return this.generateAIResearchSlides()
    }

    // Climate/Environment topics
    if (lowerTopic.includes('climate') || lowerTopic.includes('environment') ||
        lowerTopic.includes('مناخ') || lowerTopic.includes('بيئة')) {
      return this.generateClimateResearchSlides()
    }

    // Technology topics
    if (lowerTopic.includes('technology') || lowerTopic.includes('تكنولوجيا') ||
        lowerTopic.includes('تقنية')) {
      return this.generateTechnologyResearchSlides()
    }

    // Generic research template
    return this.generateGenericResearchSlides(topic)
  }

  private generatePalestineResearchSlides(): SlideData[] {
    return [
      {
        type: 'title',
        title: 'دولة فلسطين: دراسة بحثية شاملة'
      },
      {
        type: 'content',
        title: 'الجغرافيا والموقع',
        content: `# الموقع الجغرافي الاستراتيجي
- تقع في قلب الشرق الأوسط على الساحل الشرقي للبحر الأبيض المتوسط
- تربط بين قارات آسيا وأفريقيا وأوروبا
- مساحة تقدر بحوالي 27,000 كيلومتر مربع
- تضم الضفة الغربية وقطاع غزة والقدس الشرقية

# المناخ والطبيعة
- مناخ متوسطي معتدل
- تنوع جغرافي من السهول الساحلية إلى المرتفعات الجبلية
- موارد مائية محدودة تتطلب إدارة مستدامة`
      },
      {
        type: 'content',
        title: 'التاريخ والحضارة',
        content: `# الجذور التاريخية العريقة
- أرض الأنبياء والرسالات السماوية
- مهد الحضارات القديمة منذ آلاف السنين
- طريق التجارة التاريخي بين الشرق والغرب
- تراث ثقافي وديني غني ومتنوع

# المعالم التاريخية
- المسجد الأقصى وقبة الصخرة في القدس
- كنيسة المهد في بيت لحم
- المدن التاريخية مثل الخليل ونابلس وغزة
- المواقع الأثرية الكنعانية والرومانية والإسلامية`
      },
      {
        type: 'content',
        title: 'الثقافة والمجتمع',
        content: `# التنوع الثقافي
- مجتمع متعدد الأديان والثقافات
- تراث شعبي غني في الفنون والأدب والموسيقى
- الحرف التقليدية مثل التطريز والخزف والنحت
- المطبخ الفلسطيني المتميز

# التعليم والعلوم
- جامعات عريقة مثل جامعة بيرزيت والجامعة الإسلامية
- تركيز على التعليم والبحث العلمي
- إنجازات في مجالات الطب والهندسة والتكنولوجيا
- الحفاظ على اللغة العربية والتراث الثقافي`
      },
      {
        type: 'content',
        title: 'الاقتصاد والتنمية',
        content: `# القطاعات الاقتصادية
- الزراعة: زيتون، حمضيات، خضروات
- الصناعة: النسيج، الأغذية، مواد البناء
- الخدمات: السياحة الدينية والثقافية
- التكنولوجيا: نمو قطاع تكنولوجيا المعلومات

# التحديات والفرص
- الحاجة لتطوير البنية التحتية
- الاستثمار في التعليم والبحث العلمي
- تنمية القطاعات الواعدة مثل الطاقة المتجددة
- تعزيز التجارة والاستثمار الدولي`
      },
      {
        type: 'conclusion',
        title: 'الخلاصة والتوقعات المستقبلية',
        content: `- فلسطين دولة ذات تاريخ عريق وحضارة غنية
- موقع جغرافي استراتيجي يربط بين القارات
- تراث ثقافي وديني فريد يجذب العالم
- إمكانيات اقتصادية واعدة في قطاعات متنوعة
- شعب مثقف ومتعلم يسعى للتقدم والازدهار
- مستقبل مشرق يعتمد على السلام والتنمية المستدامة`
      }
    ]
  }

  private generateAIResearchSlides(): SlideData[] {
    return [
      {
        type: 'title',
        title: 'الذكاء الاصطناعي والتعلم الآلي: دراسة شاملة'
      },
      {
        type: 'content',
        title: 'مقدمة في الذكاء الاصطناعي',
        content: `# تعريف الذكاء الاصطناعي
- محاكاة الذكاء البشري في الآلات والحاسوب
- القدرة على التعلم والتفكير واتخاذ القرارات
- تطبيق الخوارزميات المتقدمة لحل المشكلات المعقدة

# أنواع الذكاء الاصطناعي
- الذكاء الاصطناعي الضيق (ANI)
- الذكاء الاصطناعي العام (AGI)
- الذكاء الاصطناعي الفائق (ASI)`
      },
      {
        type: 'content',
        title: 'التطبيقات العملية',
        content: `# المجالات الرئيسية
- الطب: تشخيص الأمراض وتطوير الأدوية
- النقل: السيارات ذاتية القيادة
- التمويل: تحليل المخاطر والتداول الآلي
- التعليم: التعلم المخصص والذكي

# التقنيات المستخدمة
- التعلم العميق (Deep Learning)
- معالجة اللغات الطبيعية (NLP)
- الرؤية الحاسوبية (Computer Vision)
- الشبكات العصبية الاصطناعية`
      },
      {
        type: 'conclusion',
        title: 'المستقبل والتحديات',
        content: `- الذكاء الاصطناعي يعيد تشكيل العالم
- فرص هائلة للتقدم في جميع المجالات
- ضرورة التعامل مع التحديات الأخلاقية
- الحاجة لتطوير المهارات والتعليم
- أهمية التعاون الدولي في التطوير والتنظيم`
      }
    ]
  }

  private generateClimateResearchSlides(): SlideData[] {
    return [
      {
        type: 'title',
        title: 'تغير المناخ والبيئة: دراسة علمية'
      },
      {
        type: 'content',
        title: 'ظاهرة تغير المناخ',
        content: `# الأسباب الرئيسية
- انبعاثات غازات الدفيئة من الأنشطة البشرية
- إزالة الغابات وتدهور النظم البيئية
- الاستخدام المفرط للوقود الأحفوري
- التصنيع والنقل والزراعة المكثفة

# التأثيرات المناخية
- ارتفاع درجات الحرارة العالمية
- ذوبان الأنهار الجليدية والقطبين
- تغير أنماط الهطول والطقس المتطرف
- ارتفاع مستوى سطح البحر`
      },
      {
        type: 'content',
        title: 'الحلول والاستراتيجيات',
        content: `# الطاقة المتجددة
- الطاقة الشمسية وطاقة الرياح
- الطاقة المائية والجيوحرارية
- تطوير تقنيات تخزين الطاقة
- تحسين كفاءة استخدام الطاقة

# السياسات البيئية
- اتفاقية باريس للمناخ
- الاقتصاد الأخضر والتنمية المستدامة
- الاستثمار في التقنيات النظيفة
- التعاون الدولي لحماية البيئة`
      },
      {
        type: 'conclusion',
        title: 'العمل المناخي المطلوب',
        content: `- تغير المناخ تحدٍ عالمي يتطلب عملاً فورياً
- الحاجة لتقليل الانبعاثات بنسبة 50% بحلول 2030
- الاستثمار في الطاقة المتجددة والتقنيات النظيفة
- أهمية التكيف مع التغيرات المناخية الحالية
- دور الأفراد والمجتمعات في الحلول البيئية`
      }
    ]
  }

  private generateTechnologyResearchSlides(): SlideData[] {
    return [
      {
        type: 'title',
        title: 'التكنولوجيا الحديثة وتأثيرها على المجتمع'
      },
      {
        type: 'content',
        title: 'التقنيات الناشئة',
        content: `# التقنيات الرائدة
- إنترنت الأشياء (IoT)
- البلوك تشين والعملات الرقمية
- الواقع المعزز والافتراضي
- الحوسبة الكمية

# التطبيقات العملية
- المدن الذكية والبيوت المتصلة
- التجارة الإلكترونية والدفع الرقمي
- التعليم الإلكتروني والعمل عن بُعد
- الطب الرقمي والصحة الذكية`
      },
      {
        type: 'content',
        title: 'التأثير الاجتماعي',
        content: `# الفوائد والمزايا
- تحسين جودة الحياة والخدمات
- زيادة الكفاءة والإنتاجية
- توفير فرص عمل جديدة
- تسهيل التواصل والتعلم

# التحديات والمخاطر
- الأمن السيبراني وحماية البيانات
- الفجوة الرقمية بين المجتمعات
- تأثير الأتمتة على سوق العمل
- الحاجة لتطوير المهارات الرقمية`
      },
      {
        type: 'conclusion',
        title: 'مستقبل التكنولوجيا',
        content: `- التكنولوجيا تعيد تشكيل طريقة عيشنا وعملنا
- ضرورة الاستثمار في التعليم التقني
- أهمية التوازن بين التقدم والأمان
- الحاجة لسياسات تنظيمية حكيمة
- التعاون الدولي لضمان الاستفادة العادلة من التقنية`
      }
    ]
  }

  private generateGenericResearchSlides(topic: string): SlideData[] {
    return [
      {
        type: 'title',
        title: `${topic}: دراسة بحثية شاملة`
      },
      {
        type: 'content',
        title: 'مقدمة ونظرة عامة',
        content: `# تعريف ${topic}
- المفاهيم الأساسية والتعريفات
- السياق التاريخي والتطور
- الأهمية في العصر الحالي
- النطاق والحدود

# أهداف الدراسة
- فهم الجوانب المختلفة للموضوع
- تحليل التحديات والفرص
- استكشاف التطبيقات العملية
- وضع توصيات للمستقبل`
      },
      {
        type: 'content',
        title: 'التحليل والدراسة',
        content: `# المنهجية البحثية
- مراجعة الأدبيات والدراسات السابقة
- جمع وتحليل البيانات
- المقابلات مع الخبراء
- دراسة الحالات العملية

# النتائج الرئيسية
- الاتجاهات والأنماط المكتشفة
- العوامل المؤثرة والمحددة
- المقارنات والتباينات
- الدروس المستفادة`
      },
      {
        type: 'content',
        title: 'التطبيقات والممارسات',
        content: `# التطبيقات العملية
- الاستخدامات في القطاعات المختلفة
- أفضل الممارسات والتجارب الناجحة
- الأدوات والتقنيات المستخدمة
- قصص النجاح والدروس المستفادة

# التحديات والحلول
- العقبات الرئيسية والصعوبات
- الاستراتيجيات للتغلب على التحديات
- الموارد المطلوبة والدعم اللازم
- خطط التطوير والتحسين`
      },
      {
        type: 'conclusion',
        title: 'الخلاصة والتوصيات',
        content: `- ${topic} موضوع ذو أهمية كبيرة ومتزايدة
- النتائج تشير إلى إمكانيات واعدة للتطوير
- ضرورة الاستثمار في البحث والتطوير
- أهمية التعاون بين الجهات المختلفة
- الحاجة لمتابعة التطورات والتحديثات المستمرة
- التوصية بإجراء دراسات أكثر تفصيلاً في المستقبل`
      }
    ]
  }
}
