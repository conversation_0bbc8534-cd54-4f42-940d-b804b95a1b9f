// Advanced Slide Interactions and Animations

export class AdvancedSlideInteractions {
  private currentSlide: number = 0
  private totalSlides: number = 0
  private isTransitioning: boolean = false
  private touchStartX: number = 0
  private touchStartY: number = 0
  private parallaxElements: NodeListOf<Element> | null = null

  constructor() {
    this.init()
  }

  /**
   * Initialize advanced interactions
   */
  init() {
    this.setupParallaxEffects()
    this.setupAdvancedNavigation()
    this.setupTouchGestures()
    this.setupKeyboardShortcuts()
    this.setupScrollIndicators()
    this.setupIntersectionObserver()
    this.setupMouseEffects()
    this.setupAdvancedAnimations()
  }

  /**
   * Setup parallax scrolling effects
   */
  setupParallaxEffects() {
    this.parallaxElements = document.querySelectorAll('[data-parallax]')
    
    window.addEventListener('scroll', () => {
      if (this.parallaxElements) {
        this.parallaxElements.forEach((element) => {
          const speed = parseFloat(element.getAttribute('data-parallax') || '0.5')
          const yPos = -(window.pageYOffset * speed)
          ;(element as HTMLElement).style.transform = `translateY(${yPos}px)`
        })
      }
    })
  }

  /**
   * Setup advanced navigation with smooth transitions
   */
  setupAdvancedNavigation() {
    const slides = document.querySelectorAll('.slide')
    this.totalSlides = slides.length

    // Create navigation dots
    this.createNavigationDots()

    // Setup slide transitions
    this.setupSlideTransitions()
  }

  /**
   * Create interactive navigation dots
   */
  createNavigationDots() {
    const dotsContainer = document.createElement('div')
    dotsContainer.className = 'slide-scroll-indicator'
    
    for (let i = 0; i < this.totalSlides; i++) {
      const dot = document.createElement('div')
      dot.className = `scroll-dot ${i === 0 ? 'active' : ''}`
      dot.addEventListener('click', () => this.goToSlide(i))
      dotsContainer.appendChild(dot)
    }
    
    document.body.appendChild(dotsContainer)
  }

  /**
   * Setup smooth slide transitions
   */
  setupSlideTransitions() {
    const slides = document.querySelectorAll('.slide')
    
    slides.forEach((slide, index) => {
      if (index > 0) {
        ;(slide as HTMLElement).style.transform = 'translateX(100%)'
        ;(slide as HTMLElement).style.opacity = '0'
      }
    })
  }

  /**
   * Navigate to specific slide
   */
  goToSlide(slideIndex: number) {
    if (this.isTransitioning || slideIndex === this.currentSlide) return
    
    this.isTransitioning = true
    const slides = document.querySelectorAll('.slide')
    const currentSlideElement = slides[this.currentSlide] as HTMLElement
    const nextSlideElement = slides[slideIndex] as HTMLElement

    // Advanced transition animation
    this.animateSlideTransition(currentSlideElement, nextSlideElement, slideIndex > this.currentSlide)

    // Update navigation dots
    this.updateNavigationDots(slideIndex)
    
    this.currentSlide = slideIndex
    
    setTimeout(() => {
      this.isTransitioning = false
    }, 800)
  }

  /**
   * Advanced slide transition animation
   */
  animateSlideTransition(currentSlide: HTMLElement, nextSlide: HTMLElement, isForward: boolean) {
    const direction = isForward ? 1 : -1
    
    // Animate current slide out
    currentSlide.style.transition = 'all 0.8s cubic-bezier(0.4, 0, 0.2, 1)'
    currentSlide.style.transform = `translateX(${-100 * direction}%) rotateY(${-15 * direction}deg)`
    currentSlide.style.opacity = '0'
    currentSlide.style.filter = 'blur(5px)'

    // Animate next slide in
    nextSlide.style.transition = 'all 0.8s cubic-bezier(0.4, 0, 0.2, 1)'
    nextSlide.style.transform = `translateX(${100 * direction}%) rotateY(${15 * direction}deg)`
    nextSlide.style.opacity = '0'
    nextSlide.style.filter = 'blur(5px)'
    
    setTimeout(() => {
      nextSlide.style.transform = 'translateX(0%) rotateY(0deg)'
      nextSlide.style.opacity = '1'
      nextSlide.style.filter = 'blur(0px)'
    }, 50)

    // Reset after animation
    setTimeout(() => {
      currentSlide.style.transition = ''
      nextSlide.style.transition = ''
    }, 800)
  }

  /**
   * Update navigation dots
   */
  updateNavigationDots(activeIndex: number) {
    const dots = document.querySelectorAll('.scroll-dot')
    dots.forEach((dot, index) => {
      dot.classList.toggle('active', index === activeIndex)
    })
  }

  /**
   * Setup touch gestures for mobile
   */
  setupTouchGestures() {
    document.addEventListener('touchstart', (e) => {
      this.touchStartX = e.touches[0].clientX
      this.touchStartY = e.touches[0].clientY
    }, { passive: true })

    document.addEventListener('touchend', (e) => {
      if (!this.touchStartX || !this.touchStartY) return

      const touchEndX = e.changedTouches[0].clientX
      const touchEndY = e.changedTouches[0].clientY
      const diffX = this.touchStartX - touchEndX
      const diffY = this.touchStartY - touchEndY

      // Horizontal swipe detection
      if (Math.abs(diffX) > Math.abs(diffY) && Math.abs(diffX) > 50) {
        if (diffX > 0) {
          // Swipe left - next slide
          this.nextSlide()
        } else {
          // Swipe right - previous slide
          this.previousSlide()
        }
      }

      this.touchStartX = 0
      this.touchStartY = 0
    }, { passive: true })
  }

  /**
   * Setup keyboard shortcuts
   */
  setupKeyboardShortcuts() {
    document.addEventListener('keydown', (e) => {
      switch (e.key) {
        case 'ArrowRight':
        case ' ':
          e.preventDefault()
          this.nextSlide()
          break
        case 'ArrowLeft':
          e.preventDefault()
          this.previousSlide()
          break
        case 'Home':
          e.preventDefault()
          this.goToSlide(0)
          break
        case 'End':
          e.preventDefault()
          this.goToSlide(this.totalSlides - 1)
          break
        case 'f':
        case 'F11':
          e.preventDefault()
          this.toggleFullscreen()
          break
      }
    })
  }

  /**
   * Setup scroll indicators
   */
  setupScrollIndicators() {
    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          const slideIndex = Array.from(document.querySelectorAll('.slide')).indexOf(entry.target)
          if (slideIndex !== -1) {
            this.updateNavigationDots(slideIndex)
            this.currentSlide = slideIndex
          }
        }
      })
    }, { threshold: 0.5 })

    document.querySelectorAll('.slide').forEach((slide) => {
      observer.observe(slide)
    })
  }

  /**
   * Setup intersection observer for animations
   */
  setupIntersectionObserver() {
    const animationObserver = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add('animate-in')
          this.triggerElementAnimations(entry.target as HTMLElement)
        }
      })
    }, { threshold: 0.2 })

    // Observe all slide elements
    document.querySelectorAll('.slide-element-interactive').forEach((element) => {
      animationObserver.observe(element)
    })
  }

  /**
   * Trigger element animations when in view
   */
  triggerElementAnimations(element: HTMLElement) {
    const children = element.querySelectorAll('.bullet-item, .slide-image, .chart-container')
    
    children.forEach((child, index) => {
      setTimeout(() => {
        child.classList.add('animate-in')
      }, index * 100)
    })
  }

  /**
   * Setup advanced mouse effects
   */
  setupMouseEffects() {
    let mouseX = 0
    let mouseY = 0

    document.addEventListener('mousemove', (e) => {
      mouseX = e.clientX
      mouseY = e.clientY

      // Parallax effect for floating elements
      const floatingElements = document.querySelectorAll('.hero-content, .content-section')
      floatingElements.forEach((element) => {
        const rect = element.getBoundingClientRect()
        const centerX = rect.left + rect.width / 2
        const centerY = rect.top + rect.height / 2
        
        const deltaX = (mouseX - centerX) * 0.01
        const deltaY = (mouseY - centerY) * 0.01
        
        ;(element as HTMLElement).style.transform += ` translate(${deltaX}px, ${deltaY}px)`
      })
    })

    // Add glow effect following mouse
    this.createMouseGlow()
  }

  /**
   * Create mouse glow effect
   */
  createMouseGlow() {
    const glow = document.createElement('div')
    glow.className = 'mouse-glow'
    glow.style.cssText = `
      position: fixed;
      width: 200px;
      height: 200px;
      background: radial-gradient(circle, rgba(102, 126, 234, 0.1) 0%, transparent 70%);
      border-radius: 50%;
      pointer-events: none;
      z-index: 9999;
      transition: opacity 0.3s ease;
      opacity: 0;
    `
    document.body.appendChild(glow)

    document.addEventListener('mousemove', (e) => {
      glow.style.left = (e.clientX - 100) + 'px'
      glow.style.top = (e.clientY - 100) + 'px'
      glow.style.opacity = '1'
    })

    document.addEventListener('mouseleave', () => {
      glow.style.opacity = '0'
    })
  }

  /**
   * Setup advanced animations
   */
  setupAdvancedAnimations() {
    // Add CSS for advanced animations
    const style = document.createElement('style')
    style.textContent = `
      .animate-in {
        animation: slideInUp 0.8s cubic-bezier(0.4, 0, 0.2, 1) forwards;
      }

      @keyframes slideInUp {
        from {
          opacity: 0;
          transform: translateY(30px) scale(0.95);
        }
        to {
          opacity: 1;
          transform: translateY(0) scale(1);
        }
      }

      .slide-element-interactive {
        opacity: 0;
        transform: translateY(30px) scale(0.95);
        transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
      }
    `
    document.head.appendChild(style)
  }

  /**
   * Navigation methods
   */
  nextSlide() {
    if (this.currentSlide < this.totalSlides - 1) {
      this.goToSlide(this.currentSlide + 1)
    }
  }

  previousSlide() {
    if (this.currentSlide > 0) {
      this.goToSlide(this.currentSlide - 1)
    }
  }

  /**
   * Toggle fullscreen mode
   */
  toggleFullscreen() {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen()
    } else {
      document.exitFullscreen()
    }
  }
}

// Auto-initialize when DOM is loaded
if (typeof window !== 'undefined') {
  document.addEventListener('DOMContentLoaded', () => {
    new AdvancedSlideInteractions()
  })
}
