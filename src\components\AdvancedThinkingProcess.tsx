'use client'

import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { advancedAIManager, ThinkingStep } from '@/lib/advanced-ai-manager'

interface AdvancedThinkingProcessProps {
  topic: string
  onComplete: (result: string) => void
  isVisible: boolean
  onClose: () => void
}

const AdvancedThinkingProcess: React.FC<AdvancedThinkingProcessProps> = ({ 
  topic, 
  onComplete, 
  isVisible, 
  onClose 
}) => {
  const [thinkingSteps, setThinkingSteps] = useState<ThinkingStep[]>([])
  const [currentStepIndex, setCurrentStepIndex] = useState(0)
  const [isProcessing, setIsProcessing] = useState(false)
  const [result, setResult] = useState<string>('')

  useEffect(() => {
    if (isVisible && topic && !isProcessing) {
      startAdvancedThinking()
    }
  }, [isVisible, topic])

  const startAdvancedThinking = async () => {
    setIsProcessing(true)
    setResult('')

    // Set up thinking callback
    advancedAIManager.setThinkingCallback((steps: ThinkingStep[]) => {
      setThinkingSteps(steps)
      
      // Find current active step
      const activeIndex = steps.findIndex(step => !step.completed && step.progress > 0)
      if (activeIndex !== -1) {
        setCurrentStepIndex(activeIndex)
      }
    })

    try {
      // Generate advanced presentation
      const presentationResult = await advancedAIManager.generateAdvancedPresentation(topic)
      setResult(presentationResult)
      
      // Wait a moment before completing
      setTimeout(() => {
        setIsProcessing(false)
        onComplete(presentationResult)
      }, 1500)
      
    } catch (error) {
      console.error('Advanced thinking process error:', error)
      setIsProcessing(false)
      onClose()
    }
  }

  const getStepIcon = (step: ThinkingStep, index: number) => {
    if (step.completed) {
      return (
        <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
          <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
          </svg>
        </div>
      )
    } else if (index === currentStepIndex && step.progress > 0) {
      return (
        <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center relative">
          <div className="animate-spin w-5 h-5 border-2 border-white border-t-transparent rounded-full" />
          <div className="absolute inset-0 rounded-full border-2 border-blue-200 animate-pulse" />
        </div>
      )
    } else {
      return (
        <div className="w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center">
          <span className="text-sm font-semibold text-gray-600 dark:text-gray-300">
            {index + 1}
          </span>
        </div>
      )
    }
  }

  const getStepEmoji = (stepType: string) => {
    const emojiMap: { [key: string]: string } = {
      'analyze': '🧠',
      'research': '🔍',
      'plan': '📋',
      'design': '🎨',
      'generate': '✍️',
      'finalize': '✅'
    }
    return emojiMap[stepType] || '🤖'
  }

  if (!isVisible) return null

  return (
    <div className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.9, y: 20 }}
        animate={{ opacity: 1, scale: 1, y: 0 }}
        exit={{ opacity: 0, scale: 0.9, y: 20 }}
        className="bg-white dark:bg-gray-800 rounded-3xl shadow-2xl max-w-4xl w-full max-h-[85vh] overflow-hidden"
      >
        {/* Header */}
        <div className="bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 text-white p-6">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-bold mb-2 flex items-center">
                🤖 WIDDX AI المتقدم يعمل...
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                  className="ml-3"
                >
                  ⚡
                </motion.div>
              </h2>
              <p className="text-blue-100">
                إنشاء عرض تقديمي ذكي ومتقدم عن: <span className="font-semibold">{topic}</span>
              </p>
            </div>
            <button
              onClick={onClose}
              className="text-white/80 hover:text-white transition-colors p-2"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        {/* Progress Overview */}
        <div className="p-6 bg-gray-50 dark:bg-gray-900 border-b">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200">
              مراحل التفكير والإنشاء
            </h3>
            <div className="text-sm text-gray-600 dark:text-gray-400">
              {thinkingSteps.filter(s => s.completed).length} / {thinkingSteps.length} مكتملة
            </div>
          </div>
          
          {/* Progress Bar */}
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3 mb-4">
            <motion.div
              className="bg-gradient-to-r from-blue-500 to-purple-500 h-3 rounded-full"
              initial={{ width: 0 }}
              animate={{ 
                width: `${(thinkingSteps.filter(s => s.completed).length / Math.max(thinkingSteps.length, 1)) * 100}%` 
              }}
              transition={{ duration: 0.5 }}
            />
          </div>

          {/* Step Icons */}
          <div className="flex items-center justify-between">
            {thinkingSteps.map((step, index) => (
              <div key={step.step} className="flex flex-col items-center">
                {getStepIcon(step, index)}
                <span className="text-xs mt-2 text-center max-w-16 text-gray-600 dark:text-gray-400">
                  {getStepEmoji(step.step)}
                </span>
              </div>
            ))}
          </div>
        </div>

        {/* Detailed Steps */}
        <div className="p-6 max-h-[50vh] overflow-y-auto">
          <div className="space-y-4">
            {thinkingSteps.map((step, index) => (
              <motion.div
                key={step.step}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
                className={`border rounded-xl p-4 transition-all duration-300 ${
                  step.completed
                    ? 'border-green-200 bg-green-50 dark:bg-green-900/20 dark:border-green-800'
                    : index === currentStepIndex && step.progress > 0
                    ? 'border-blue-200 bg-blue-50 dark:bg-blue-900/20 dark:border-blue-800 shadow-lg'
                    : 'border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800'
                }`}
              >
                <div className="flex items-center justify-between mb-3">
                  <h4 className={`font-semibold text-lg ${
                    step.completed
                      ? 'text-green-700 dark:text-green-300'
                      : index === currentStepIndex && step.progress > 0
                      ? 'text-blue-700 dark:text-blue-300'
                      : 'text-gray-600 dark:text-gray-400'
                  }`}>
                    {step.description}
                  </h4>
                  
                  <div className="flex items-center space-x-2">
                    {step.progress > 0 && step.progress < 100 && (
                      <div className="text-sm font-medium text-blue-600 dark:text-blue-400">
                        {Math.round(step.progress)}%
                      </div>
                    )}
                    {getStepIcon(step, index)}
                  </div>
                </div>

                {/* Progress Bar for Active Step */}
                {index === currentStepIndex && step.progress > 0 && step.progress < 100 && (
                  <div className="mb-3">
                    <div className="w-full bg-blue-100 dark:bg-blue-900 rounded-full h-2">
                      <motion.div
                        className="bg-blue-500 h-2 rounded-full"
                        initial={{ width: 0 }}
                        animate={{ width: `${step.progress}%` }}
                        transition={{ duration: 0.3 }}
                      />
                    </div>
                  </div>
                )}

                {/* Step Details */}
                <AnimatePresence>
                  {step.details && step.details.length > 0 && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: 'auto' }}
                      exit={{ opacity: 0, height: 0 }}
                      className="space-y-2"
                    >
                      {step.details.map((detail, detailIndex) => (
                        <motion.div
                          key={detailIndex}
                          initial={{ opacity: 0, x: -10 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: detailIndex * 0.1 }}
                          className="text-sm text-gray-700 dark:text-gray-300 flex items-start"
                        >
                          <motion.span
                            initial={{ scale: 0 }}
                            animate={{ scale: 1 }}
                            transition={{ delay: detailIndex * 0.1 + 0.2 }}
                            className="w-2 h-2 bg-blue-400 rounded-full mr-3 mt-2 flex-shrink-0"
                          />
                          <span className="leading-relaxed">{detail}</span>
                        </motion.div>
                      ))}
                    </motion.div>
                  )}
                </AnimatePresence>
              </motion.div>
            ))}
          </div>
        </div>

        {/* Footer */}
        <div className="bg-gray-50 dark:bg-gray-900 p-4 text-center border-t">
          <motion.p
            animate={{ opacity: [0.5, 1, 0.5] }}
            transition={{ duration: 2, repeat: Infinity }}
            className="text-sm text-gray-600 dark:text-gray-400"
          >
            {isProcessing 
              ? 'جاري العمل على إنشاء عرض تقديمي استثنائي بتقنيات الذكاء الاصطناعي المتقدمة...' 
              : 'تم الانتهاء من إنشاء العرض التقديمي!'
            }
          </motion.p>
        </div>
      </motion.div>
    </div>
  )
}

export default AdvancedThinkingProcess
