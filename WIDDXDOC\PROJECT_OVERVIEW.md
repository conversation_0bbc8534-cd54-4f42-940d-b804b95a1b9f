# Next.js Chat Application - Project Overview

## System Architecture
- **Frontend**: Next.js 14 with App Router
- **Backend**: Next.js API routes
- **AI Services**: 
  - DeepSeek (primary)
  - Gemini (fallback)
- **Database**: MongoDB (via Mongoose)
- **Authentication**: NextAuth.js

## Key Components

### Core Modules
1. **Chat Interface**
   - Real-time messaging
   - Message history
   - Context-aware responses

2. **Slide Generation System**
   - Basic generator (text-based slides)
   - Advanced generator (charts, comparisons)
   - Template system (HTML/CSS)

3. **Localization System**
   - English/Arabic support
   - RTL/LTR layout handling
   - Dynamic content switching

4. **AI Integration Layer**
   - Service abstraction
   - Fallback mechanisms
   - Response formatting

## Data Flow
1. User → Chat Interface → API Routes
2. API Routes → AI Services → Response Processing
3. Response → Slide Generation → UI Rendering

## Technology Stack
- **Frontend**: React, TypeScript, Tailwind CSS
- **Backend**: Node.js, Next.js API
- **Utilities**: <PERSON><PERSON> (validation), i18next (localization)
- **Build**: Vite, ESLint, Prettier

## Feature Overview
- Real-time AI-powered chat
- Dynamic slide generation
- Multi-language support
- PDF/HTML export
- Responsive design