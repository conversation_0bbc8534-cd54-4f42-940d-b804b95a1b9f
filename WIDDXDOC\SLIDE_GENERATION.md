# Slide Generation System

## Overview
The slide generation system provides dynamic HTML-based presentation creation with:
- Template-based slide generation
- Responsive design
- Interactive elements
- Export to HTML/PDF
- RTL/LTR language support

## Core Components

### 1. Slide Generators
- **Basic Generator** (`slide-generator.ts`):
  - Simple template-based generation
  - Fallback content for common topics
  - Basic slide types (title, content, conclusion)

- **Advanced Generator** (`slide-generator-advanced.ts`):
  - Support for charts and comparison slides
  - Better RTL/Arabic support
  - More sophisticated template system
  - Dynamic content formatting

### 2. Template System (`slide-templates.ts`)
- Predefined templates with:
  - CSS styles
  - HTML structures
  - Color schemes
  - Font configurations

Key template types:
- `arabic-professional`: RTL Arabic template
- `modern-business`: Clean business style
- `academic-research`: Formal academic style

### 3. UI Integration (`SlidePanel.tsx`)
- Slide preview with navigation
- HTML source view
- Export options:
  - Copy HTML
  - Download HTML file
  - Export to PDF

## Slide Types

### Basic Slides
1. **Title Slide**: Presentation title and metadata
2. **Content Slide**: Text content with formatting
3. **Conclusion Slide**: Summary points

### Advanced Slides
1. **Chart Slide**: Interactive data visualization
2. **Comparison Slide**: Feature/price comparisons
3. **Dynamic Content Slide**: Formatted lists and sections

## Fallback System
When AI services are unavailable:
1. Attempts to use advanced generator first
2. Falls back to basic generator
3. Provides topic-specific content for:
   - AI/Machine Learning
   - Renewable Energy
   - Generic topics

## Usage Example

```typescript
// Basic generation
const generator = new SlideGenerator('modern-business')
const html = generator.generatePresentation({
  title: 'My Presentation',
  slides: [
    { type: 'title', title: 'Welcome' },
    { type: 'content', title: 'Introduction', content: '...' }
  ],
  templateId: 'modern-business'
})

// Advanced generation 
const advancedGenerator = new AdvancedSlideGenerator('arabic-professional')
const advancedHtml = advancedGenerator.generatePresentation({
  title: 'عرض تقديمي',
  template: 'arabic-professional',
  language: 'ar',
  slides: [
    { 
      type: 'chart', 
      title: 'البيانات',
      chartData: { /* ... */ }
    }
  ]
})
```

## Export Options
```typescript
// From SlidePanel component
await exportPresentationToPDF(html, 'presentation.pdf', {
  format: 'Slide',
  orientation: 'landscape'
})
```

## Template Development
To create new templates:
1. Add to `slide-templates.ts`
2. Define:
   - CSS styles
   - Slide structures
   - Color scheme
   - Required dependencies

Example template definition:
```typescript
{
  id: 'new-template',
  name: 'New Template',
  description: '...',
  css: `/* styles */`,
  structure: {
    titleSlide: `<!-- HTML -->`,
    // other slide types
  }
}