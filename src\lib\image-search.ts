// Advanced Image Search System for AI Slides

interface UnsplashImage {
  id: string
  urls: {
    raw: string
    full: string
    regular: string
    small: string
    thumb: string
  }
  alt_description: string
  description: string
  user: {
    name: string
    username: string
  }
  color: string
  width: number
  height: number
}

interface ImageSearchResult {
  url: string
  alt: string
  description: string
  color: string
  photographer: string
  width: number
  height: number
}

class ImageSearchService {
  private readonly UNSPLASH_ACCESS_KEY = 'YOUR_UNSPLASH_ACCESS_KEY' // يجب الحصول على مفتاح من Unsplash
  private readonly FALLBACK_IMAGES = {
    palestine: [
      'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=800',
      'https://images.unsplash.com/photo-1544161515-4ab6ce6db874?w=800',
      'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=800'
    ],
    technology: [
      'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=800',
      'https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=800',
      'https://images.unsplash.com/photo-1485827404703-89b55fcc595e?w=800'
    ],
    business: [
      'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=800',
      'https://images.unsplash.com/photo-1454165804606-c3d57bc86b40?w=800',
      'https://images.unsplash.com/photo-1556761175-b413da4baf72?w=800'
    ],
    education: [
      'https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=800',
      'https://images.unsplash.com/photo-1503676260728-1c00da094a0b?w=800',
      'https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=800'
    ],
    science: [
      'https://images.unsplash.com/photo-1532094349884-543bc11b234d?w=800',
      'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=800',
      'https://images.unsplash.com/photo-1581833971358-2c8b550f87b3?w=800'
    ]
  }

  /**
   * Generate smart keywords based on topic
   */
  private generateKeywords(topic: string): string[] {
    const topicLower = topic.toLowerCase()
    
    // Arabic to English keyword mapping
    const keywordMap: { [key: string]: string[] } = {
      'فلسطين': ['palestine', 'jerusalem', 'middle east', 'olive tree', 'dome rock'],
      'تكنولوجيا': ['technology', 'computer', 'digital', 'innovation', 'coding'],
      'تعليم': ['education', 'learning', 'school', 'university', 'books'],
      'علوم': ['science', 'research', 'laboratory', 'microscope', 'experiment'],
      'اقتصاد': ['business', 'finance', 'economy', 'growth', 'market'],
      'تاريخ': ['history', 'ancient', 'culture', 'heritage', 'monument'],
      'جغرافيا': ['geography', 'map', 'landscape', 'nature', 'earth'],
      'طب': ['medicine', 'health', 'doctor', 'hospital', 'medical'],
      'رياضة': ['sports', 'fitness', 'athlete', 'competition', 'exercise']
    }

    // Find matching keywords
    for (const [arabic, english] of Object.entries(keywordMap)) {
      if (topicLower.includes(arabic)) {
        return english
      }
    }

    // English keywords
    const englishKeywords: { [key: string]: string[] } = {
      'artificial intelligence': ['ai', 'robot', 'machine learning', 'neural network'],
      'climate change': ['environment', 'nature', 'green energy', 'sustainability'],
      'space': ['astronomy', 'planet', 'galaxy', 'rocket', 'universe'],
      'ocean': ['sea', 'marine', 'underwater', 'coral reef', 'waves']
    }

    for (const [key, keywords] of Object.entries(englishKeywords)) {
      if (topicLower.includes(key)) {
        return keywords
      }
    }

    // Default keywords based on topic analysis
    return this.analyzeTopicForKeywords(topic)
  }

  private analyzeTopicForKeywords(topic: string): string[] {
    const words = topic.toLowerCase().split(/\s+/)
    const keywords: string[] = []

    // Add the topic itself
    keywords.push(topic)

    // Add related general keywords
    if (words.some(w => ['research', 'study', 'analysis', 'بحث', 'دراسة'].includes(w))) {
      keywords.push('research', 'study', 'analysis', 'data')
    }

    if (words.some(w => ['presentation', 'عرض', 'تقديمي'].includes(w))) {
      keywords.push('presentation', 'business', 'professional')
    }

    return keywords.slice(0, 5) // Limit to 5 keywords
  }

  /**
   * Search for images using Unsplash API
   */
  async searchImages(topic: string, count: number = 5): Promise<ImageSearchResult[]> {
    const keywords = this.generateKeywords(topic)
    const results: ImageSearchResult[] = []

    try {
      // Try Unsplash API first
      if (this.UNSPLASH_ACCESS_KEY && this.UNSPLASH_ACCESS_KEY !== 'YOUR_UNSPLASH_ACCESS_KEY') {
        const unsplashResults = await this.searchUnsplash(keywords[0], count)
        results.push(...unsplashResults)
      }

      // If no results or API key not available, use fallback
      if (results.length === 0) {
        const fallbackResults = this.getFallbackImages(topic, count)
        results.push(...fallbackResults)
      }

      return results.slice(0, count)
    } catch (error) {
      console.error('Image search error:', error)
      return this.getFallbackImages(topic, count)
    }
  }

  private async searchUnsplash(query: string, count: number): Promise<ImageSearchResult[]> {
    const response = await fetch(
      `https://api.unsplash.com/search/photos?query=${encodeURIComponent(query)}&per_page=${count}&orientation=landscape`,
      {
        headers: {
          'Authorization': `Client-ID ${this.UNSPLASH_ACCESS_KEY}`
        }
      }
    )

    if (!response.ok) {
      throw new Error('Unsplash API error')
    }

    const data = await response.json()
    
    return data.results.map((image: UnsplashImage): ImageSearchResult => ({
      url: image.urls.regular,
      alt: image.alt_description || image.description || 'Image',
      description: image.description || image.alt_description || '',
      color: image.color,
      photographer: image.user.name,
      width: image.width,
      height: image.height
    }))
  }

  private getFallbackImages(topic: string, count: number): ImageSearchResult[] {
    const topicLower = topic.toLowerCase()
    let imageUrls: string[] = []

    // Determine category
    if (topicLower.includes('فلسطين') || topicLower.includes('palestine')) {
      imageUrls = this.FALLBACK_IMAGES.palestine
    } else if (topicLower.includes('تكنولوجيا') || topicLower.includes('technology')) {
      imageUrls = this.FALLBACK_IMAGES.technology
    } else if (topicLower.includes('اقتصاد') || topicLower.includes('business')) {
      imageUrls = this.FALLBACK_IMAGES.business
    } else if (topicLower.includes('تعليم') || topicLower.includes('education')) {
      imageUrls = this.FALLBACK_IMAGES.education
    } else if (topicLower.includes('علوم') || topicLower.includes('science')) {
      imageUrls = this.FALLBACK_IMAGES.science
    } else {
      // Default to business images
      imageUrls = this.FALLBACK_IMAGES.business
    }

    return imageUrls.slice(0, count).map((url, index) => ({
      url,
      alt: `${topic} - Image ${index + 1}`,
      description: `Professional image related to ${topic}`,
      color: '#4A90E2',
      photographer: 'Unsplash',
      width: 800,
      height: 600
    }))
  }

  /**
   * Get hero image for title slide
   */
  async getHeroImage(topic: string): Promise<ImageSearchResult> {
    const images = await this.searchImages(topic, 1)
    return images[0] || {
      url: 'https://images.unsplash.com/photo-1557804506-669a67965ba0?w=1200',
      alt: 'Professional background',
      description: 'Professional presentation background',
      color: '#4A90E2',
      photographer: 'Unsplash',
      width: 1200,
      height: 800
    }
  }

  /**
   * Get content images for slides
   */
  async getContentImages(topic: string, slideCount: number): Promise<ImageSearchResult[]> {
    return await this.searchImages(topic, slideCount)
  }

  /**
   * Generate image for specific slide content
   */
  async getSlideImage(slideTitle: string, slideContent: string): Promise<ImageSearchResult> {
    // Combine slide title and content for better keyword generation
    const combinedText = `${slideTitle} ${slideContent}`
    const keywords = this.generateKeywords(combinedText)
    
    const images = await this.searchImages(keywords[0] || slideTitle, 1)
    return images[0] || await this.getHeroImage(slideTitle)
  }
}

// Export singleton instance
export const imageSearchService = new ImageSearchService()

// Export types
export type { ImageSearchResult }
