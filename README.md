# WIDDX AI Chat Interface

A modern, responsive AI chat interface built with Next.js 14, featuring multiple AI models, bilingual support, and a sleek design inspired by Z.ai.

## ✅ Status: FULLY FUNCTIONAL

The application is now fully functional with secure backend API integration. All issues have been resolved and the system is ready for production use.

## 🚀 Features

- **✅ Secure Backend API**: All AI integrations moved to secure server-side endpoints
- **✅ Multiple AI Models**: Integration with DeepSeek and Gemini AI with automatic fallback
- **✅ Bilingual Support**: Full English and Arabic language support
- **✅ Multiple Chat Modes**: Chat, Code, Slides, Writer, Research, and Summarizer
- **✅ Dark/Light Theme**: Toggle between themes with smooth transitions
- **✅ Responsive Design**: Works perfectly on desktop and mobile devices
- **✅ Real-time Streaming**: Live AI responses with typing indicators
- **✅ Chat Management**: Create, delete, and organize multiple conversations
- **✅ Local Storage**: Persistent chat history across sessions
- **✅ Rate Limiting**: Built-in request throttling and usage control
- **✅ Health Monitoring**: API service health checks and status monitoring
- **✅ Error Handling**: Graceful fallback responses when services are unavailable

## 🛠 Tech Stack

- **Framework**: Next.js 14 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **Icons**: Lucide React
- **AI Integration**: DeepSeek API, Gemini API (Server-side)
- **State Management**: React Hooks
- **Storage**: Local Storage
- **API**: RESTful endpoints with streaming support

## 🚀 Getting Started

### Prerequisites

- Node.js 18+ 
- npm or yarn
- API keys for DeepSeek and/or Gemini

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd widdx-ai-chat
```

2. Install dependencies:
```bash
npm install
```

3. Create a `.env.local` file in the root directory:
```env
# WIDDX AI Configuration
NEXT_PUBLIC_APP_NAME=WIDDX AI
NEXT_PUBLIC_COMPANY_NAME=WIDDX

# API Keys (Add your actual keys here)
DEEPSEEK_API_KEY=your_deepseek_api_key_here
GEMINI_API_KEY=your_gemini_api_key_here

# API Endpoints
DEEPSEEK_API_URL=https://api.deepseek.com/v1
GEMINI_API_URL=https://generativelanguage.googleapis.com/v1beta

# Model Configuration
DEFAULT_MODEL=deepseek
FALLBACK_MODEL=gemini
MAX_TOKENS=4000
TEMPERATURE=0.7

# Rate Limiting
RATE_LIMIT_REQUESTS_PER_MINUTE=60
RATE_LIMIT_TOKENS_PER_MINUTE=100000

# Security
JWT_SECRET=your_jwt_secret_here
ENCRYPTION_KEY=your_encryption_key_here

# Logging
LOG_LEVEL=info
ENABLE_ANALYTICS=true
```

4. Run the development server:
```bash
npm run dev
```

5. Open [http://localhost:3000](http://localhost:3000) in your browser.

## 🔌 API Endpoints

The application provides secure server-side API endpoints:

- `POST /api/chat` - Send messages to AI models (with fallback support)
- `POST /api/chat/stream` - Stream AI responses in real-time using Server-Sent Events
- `GET /api/health` - Check the health status of AI services and system status

### API Usage Examples

#### Send a Chat Message
```bash
curl -X POST http://localhost:3000/api/chat \
  -H "Content-Type: application/json" \
  -d '{
    "messages": [{"role": "user", "content": "Hello!"}],
    "mode": "chat",
    "locale": "en",
    "userId": "user123"
  }'
```

#### Check System Health
```bash
curl -X GET http://localhost:3000/api/health
```

## ⚙️ Configuration

### Environment Variables

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `DEEPSEEK_API_KEY` | DeepSeek API key | - | Yes |
| `GEMINI_API_KEY` | Gemini API key | - | Yes |
| `DEEPSEEK_API_URL` | DeepSeek API base URL | `https://api.deepseek.com/v1` | No |
| `GEMINI_API_URL` | Gemini API base URL | `https://generativelanguage.googleapis.com/v1beta` | No |
| `DEFAULT_MODEL` | Primary AI model | `deepseek` | No |
| `FALLBACK_MODEL` | Fallback AI model | `gemini` | No |
| `MAX_TOKENS` | Maximum tokens per request | `4000` | No |
| `TEMPERATURE` | AI response creativity | `0.7` | No |
| `RATE_LIMIT_REQUESTS_PER_MINUTE` | Request rate limit | `60` | No |

### Chat Modes

The application supports different chat modes, each optimized for specific use cases:

- **Chat**: General conversation and assistance
- **Code**: Programming help, debugging, and code generation
- **Slides**: Presentation creation and structuring
- **Writer**: Content writing and editing assistance
- **Research**: Research assistance and information gathering
- **Summarizer**: Text summarization and key point extraction

## 🏗 Architecture

### Secure Backend Architecture

The application now uses a secure three-tier architecture:

1. **Frontend (Client-side)**: React components and user interface
2. **API Layer (Server-side)**: Next.js API routes handling AI requests
3. **AI Services (Server-side)**: DeepSeek and Gemini integrations

### Key Components

#### Frontend Components
- `ChatInterface`: Main chat interface component
- `Sidebar`: Navigation and chat history
- `MessageList`: Display chat messages with streaming support
- `MessageInput`: Input field for new messages
- `ThemeToggle`: Dark/light theme switcher
- `LanguageToggle`: English/Arabic language switcher

#### Backend Services
- `AIManager`: Orchestrates AI model requests with fallback logic
- `DeepSeekService`: Secure DeepSeek API integration
- `GeminiService`: Secure Gemini API integration
- `APIClient`: Frontend-to-backend communication service

#### API Routes
- `/api/chat/route.ts`: Main chat endpoint with error handling
- `/api/chat/stream/route.ts`: Streaming responses using SSE
- `/api/health/route.ts`: System health monitoring

### Security Features

- **🔒 Server-side API Keys**: All API keys are secured server-side
- **🛡️ Input Validation**: Request validation and sanitization
- **⚡ Rate Limiting**: Built-in request throttling
- **🔄 Automatic Fallback**: Seamless switching between AI models
- **📊 Health Monitoring**: Real-time service status tracking
- **🚨 Error Handling**: Graceful error recovery with user-friendly messages

## 🚀 Development

### Project Structure

```
src/
├── app/
│   ├── api/           # Secure API routes
│   │   ├── chat/      # Chat endpoints
│   │   └── health/    # Health check endpoint
│   ├── globals.css    # Global styles
│   ├── layout.tsx     # Root layout
│   └── page.tsx       # Home page
├── components/        # React components
├── hooks/            # Custom React hooks (updated for API client)
├── lib/              # Utility libraries and services
│   ├── ai-manager.ts  # AI orchestration service
│   ├── api-client.ts  # Frontend API client
│   ├── deepseek.ts    # DeepSeek service
│   └── gemini.ts      # Gemini service
└── types/            # TypeScript type definitions
```

### Building for Production

```bash
npm run build
npm start
```

### Testing the API

The application includes comprehensive error handling and fallback mechanisms. Even if API keys are not configured, the system will provide helpful fallback responses.

## 🔧 Troubleshooting

### Common Issues

1. **API Keys Not Working**: 
   - Verify your API keys in `.env.local`
   - Check the health endpoint: `GET /api/health`
   - The system will use fallback responses if keys are invalid

2. **Build Errors**:
   - Run `npm run build` to check for TypeScript errors
   - All current issues have been resolved

3. **Runtime Errors**:
   - Check the browser console for client-side errors
   - Check the server terminal for API errors
   - The system includes comprehensive error handling

## 📝 Recent Updates

### ✅ Fixed Issues (Latest)

1. **✅ Secure API Integration**: Moved all AI service calls to secure backend endpoints
2. **✅ Code Cleanup**: Removed unused simulation functions from useChat hook
3. **✅ Error Handling**: Improved error handling with graceful fallbacks
4. **✅ API Client**: Created dedicated API client for frontend-backend communication
5. **✅ Health Monitoring**: Added comprehensive health check endpoints
6. **✅ Streaming Support**: Implemented Server-Sent Events for real-time responses
7. **✅ Rate Limiting**: Added request throttling and usage control
8. **✅ TypeScript Errors**: Resolved all compilation and runtime errors

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For support, please open an issue on GitHub or contact the WIDDX development team.

---

**Status**: ✅ **READY FOR PRODUCTION** - All issues resolved, secure backend implemented, comprehensive error handling in place.
