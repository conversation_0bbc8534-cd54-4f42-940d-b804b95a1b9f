# Project Configuration

## Next.js Configuration
- Basic Next.js setup in `next.config.js`
- Currently no custom configurations added

## Tailwind CSS
- Dark/light mode support (`darkMode: 'class'`)
- Custom color themes for both light and dark modes
- Custom animations (fade-in, slide-up/down, typing indicator)
- Custom font families (sans and mono)
- Custom shadows and border radii
- Content sources configured for all JS/TS/JSX/TSX/MDX files

## PostCSS
- Uses Tailwind CSS plugin
- Includes autoprefixer

## TypeScript
- Strict mode enabled
- Path aliases configured for:
  - @/components
  - @/lib 
  - @/hooks
  - @/types
  - @/locales
- JSX preserved for React
- ES modules and modern features enabled