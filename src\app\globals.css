@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom CSS Variables for WIDDX AI exact colors */
:root {
  /* Light theme - exact WIDDX AI colors */
  --color-bg-primary: #F4F6F8;
  --color-bg-secondary: #ffffff;
  --color-bg-tertiary: #f8fafc;
  --color-bg-hover: #f1f5f9;
  --color-text-primary: #1a1a1a;
  --color-text-secondary: #6b7280;
  --color-text-muted: #9ca3af;
  --color-border: #e5e7eb;
  --color-border-hover: #d1d5db;
  --color-accent: #3b82f6;
  --color-accent-hover: #2563eb;
  --color-accent-light: rgba(59, 130, 246, 0.1);
  --color-success: #10b981;
  --color-warning: #f59e0b;
  --color-error: #ef4444;
  --color-shadow: rgba(0, 0, 0, 0.04);
  --color-shadow-medium: rgba(0, 0, 0, 0.08);
  --color-shadow-strong: rgba(0, 0, 0, 0.12);
}

/* Dark theme - exact WIDDX AI colors */
.dark {
  --color-bg-primary: #141618;
  --color-bg-secondary: #1f2937;
  --color-bg-tertiary: #374151;
  --color-bg-hover: #4b5563;
  --color-text-primary: #f9fafb;
  --color-text-secondary: #d1d5db;
  --color-text-muted: #9ca3af;
  --color-border: #374151;
  --color-border-hover: #4b5563;
  --color-accent: #60a5fa;
  --color-accent-hover: #3b82f6;
  --color-accent-light: rgba(96, 165, 250, 0.1);
  --color-success: #10b981;
  --color-warning: #f59e0b;
  --color-error: #ef4444;
  --color-shadow: rgba(0, 0, 0, 0.3);
  --color-shadow-medium: rgba(0, 0, 0, 0.4);
  --color-shadow-strong: rgba(0, 0, 0, 0.5);
}

/* Base styles */
* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.6;
  color: var(--color-text-primary);
  background-color: var(--color-bg-primary);
  transition: background-color 0.3s ease, color 0.3s ease;
}

body {
  color: var(--color-text-primary);
  background: var(--color-bg-primary);
}

/* RTL Support for Arabic */
[dir="rtl"] {
  direction: rtl;
  text-align: right;
}

/* RTL Flexbox adjustments */
[dir="rtl"] .flex-row {
  flex-direction: row-reverse;
}

[dir="rtl"] .flex-row-reverse {
  flex-direction: row;
}

/* RTL Margin adjustments */
[dir="rtl"] .ml-auto {
  margin-left: 0;
  margin-right: auto;
}

[dir="rtl"] .mr-auto {
  margin-right: 0;
  margin-left: auto;
}

[dir="rtl"] .ml-2 {
  margin-left: 0;
  margin-right: 0.5rem;
}

[dir="rtl"] .mr-2 {
  margin-right: 0;
  margin-left: 0.5rem;
}

[dir="rtl"] .ml-3 {
  margin-left: 0;
  margin-right: 0.75rem;
}

[dir="rtl"] .mr-3 {
  margin-right: 0;
  margin-left: 0.75rem;
}

[dir="rtl"] .ml-4 {
  margin-left: 0;
  margin-right: 1rem;
}

[dir="rtl"] .mr-4 {
  margin-right: 0;
  margin-left: 1rem;
}

/* RTL Padding adjustments */
[dir="rtl"] .pl-2 {
  padding-left: 0;
  padding-right: 0.5rem;
}

[dir="rtl"] .pr-2 {
  padding-right: 0;
  padding-left: 0.5rem;
}

[dir="rtl"] .pl-3 {
  padding-left: 0;
  padding-right: 0.75rem;
}

[dir="rtl"] .pr-3 {
  padding-right: 0;
  padding-left: 0.75rem;
}

[dir="rtl"] .pl-4 {
  padding-left: 0;
  padding-right: 1rem;
}

[dir="rtl"] .pr-4 {
  padding-right: 0;
  padding-left: 1rem;
}

/* RTL Border adjustments */
[dir="rtl"] .border-l {
  border-left: 0;
  border-right: 1px solid;
}

[dir="rtl"] .border-r {
  border-right: 0;
  border-left: 1px solid;
}

/* RTL Position adjustments */
[dir="rtl"] .left-0 {
  left: auto;
  right: 0;
}

[dir="rtl"] .right-0 {
  right: auto;
  left: 0;
}

/* RTL Text alignment */
[dir="rtl"] .text-left {
  text-align: right;
}

[dir="rtl"] .text-right {
  text-align: left;
}

/* RTL Specific layout fixes */
.rtl .flex {
  direction: rtl;
}

.rtl .flex-row {
  flex-direction: row-reverse;
}

.rtl .flex-row-reverse {
  flex-direction: row;
}

/* RTL Sidebar positioning */
[dir="rtl"] .sidebar {
  right: 0;
  left: auto;
}

/* RTL Message bubbles */
[dir="rtl"] .message-user {
  margin-left: 0;
  margin-right: auto;
}

[dir="rtl"] .message-assistant {
  margin-right: 0;
  margin-left: auto;
}

/* RTL Input area */
[dir="rtl"] .input-area {
  direction: rtl;
}

[dir="rtl"] input, [dir="rtl"] textarea {
  text-align: right;
}

/* RTL Button positioning */
[dir="rtl"] .space-x-2 > * + * {
  margin-left: 0;
  margin-right: 0.5rem;
}

[dir="rtl"] .space-x-3 > * + * {
  margin-left: 0;
  margin-right: 0.75rem;
}

[dir="rtl"] .space-x-4 > * + * {
  margin-left: 0;
  margin-right: 1rem;
}

/* Scrollbar styling to match Widdx.ai*/
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: var(--color-border);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-border-hover);
}

/* Custom animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInFromRight {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0);
  }
}

@keyframes slideInFromLeft {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

/* Message animations */
.message-enter {
  animation: fadeInUp 0.3s ease-out;
}

.message-typing {
  animation: fadeIn 0.3s ease-out;
}

/* Typing indicator dots */
.typing-dot {
  animation: typing 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) {
  animation-delay: -0.32s;
}

.typing-dot:nth-child(2) {
  animation-delay: -0.16s;
}

.typing-dot:nth-child(3) {
  animation-delay: 0s;
}

/* Loading shimmer effect */
.shimmer {
  background: linear-gradient(
    90deg,
    var(--color-bg-secondary) 0%,
    var(--color-bg-hover) 50%,
    var(--color-bg-secondary) 100%
  );
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

/* Focus styles */
.focus-ring:focus {
  outline: 2px solid var(--color-accent);
  outline-offset: 2px;
}

.focus-ring:focus-visible {
  outline: 2px solid var(--color-accent);
  outline-offset: 2px;
}

/* Button hover effects */
.btn-hover {
  transition: all 0.2s ease;
}

.btn-hover:hover {
  transform: translateY(-1px);
}

.btn-hover:active {
  transform: translateY(0);
}

/* Custom utilities */
.text-gradient {
  background: linear-gradient(135deg, var(--color-accent), var(--color-accent-hover));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.glass-effect {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.dark .glass-effect {
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Selection styling */
::selection {
  background: var(--color-accent-light);
  color: var(--color-text-primary);
}

/* Disable text selection on UI elements */
.no-select {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Custom markdown styles */
.markdown-content {
  line-height: 1.7;
}

.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
  margin-top: 1.5em;
  margin-bottom: 0.5em;
  font-weight: 600;
  color: var(--color-text-primary);
}

.markdown-content p {
  margin-bottom: 1em;
}

.markdown-content code {
  background: var(--color-bg-tertiary);
  padding: 0.2em 0.4em;
  border-radius: 0.25rem;
  font-size: 0.875em;
  font-family: var(--font-mono);
}

.markdown-content pre {
  background: var(--color-bg-tertiary);
  padding: 1rem;
  border-radius: 0.5rem;
  overflow-x: auto;
  margin: 1em 0;
}

.markdown-content pre code {
  background: none;
  padding: 0;
}

.markdown-content ul,
.markdown-content ol {
  margin-left: 1.5em;
  margin-bottom: 1em;
}

.markdown-content li {
  margin-bottom: 0.25em;
}

.markdown-content blockquote {
  border-left: 4px solid var(--color-accent);
  padding-left: 1rem;
  margin: 1em 0;
  font-style: italic;
  color: var(--color-text-secondary);
}

.markdown-content table {
  width: 100%;
  border-collapse: collapse;
  margin: 1em 0;
}

.markdown-content th,
.markdown-content td {
  border: 1px solid var(--color-border);
  padding: 0.5rem;
  text-align: left;
}

.markdown-content th {
  background: var(--color-bg-tertiary);
  font-weight: 600;
}

/* Slide Panel Animations */
@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOutRight {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

.animate-slide-in-right {
  animation: slideInRight 0.3s ease-out forwards;
}

.animate-slide-out-right {
  animation: slideOutRight 0.3s ease-in forwards;
}

/* Enhanced UI Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.4s ease-out forwards;
}

.animate-pulse-slow {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Smooth transitions for all interactive elements */
.transition-smooth {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhanced hover effects */
.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.hover-glow:hover {
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
}

/* Enhanced Mobile Responsive Design */
/* Small devices (phones, 576px and up) */
@media (max-width: 575.98px) {
  .mobile-hidden {
    display: none !important;
  }

  .mobile-full {
    width: 100% !important;
  }

  .mobile-padding {
    padding: 0.75rem !important;
  }

  .mobile-text-sm {
    font-size: 0.875rem !important;
  }

  .mobile-h-12 {
    height: 3rem !important;
  }

  /* Mobile sidebar - full overlay */
  .mobile-sidebar-overlay {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    width: 100% !important;
    z-index: 60 !important;
  }

  /* Mobile slide panel - full screen */
  .mobile-slide-panel {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    width: 100% !important;
    z-index: 55 !important;
  }

  /* Mobile chat input */
  .mobile-chat-input {
    padding: 0.5rem !important;
    font-size: 1rem !important;
    min-height: 2.5rem !important;
  }

  /* Mobile header */
  .mobile-header-compact {
    padding: 0.5rem 1rem !important;
    height: 3.5rem !important;
  }

  /* Mobile message spacing */
  .mobile-message-spacing {
    margin-bottom: 1rem !important;
  }

  /* Mobile button sizing */
  .mobile-btn-sm {
    padding: 0.5rem 0.75rem !important;
    font-size: 0.875rem !important;
  }
}

/* Medium devices (tablets, 768px and up) */
@media (min-width: 576px) and (max-width: 991.98px) {
  .tablet-hidden {
    display: none !important;
  }

  .tablet-sidebar-width {
    width: 20rem !important;
  }

  .tablet-slide-panel {
    width: 50% !important;
  }

  .tablet-padding {
    padding: 1rem !important;
  }
}

/* Large devices (desktops, 992px and up) */
@media (min-width: 992px) {
  .desktop-sidebar-width {
    width: 20rem !important;
  }

  .desktop-slide-panel {
    width: 50% !important;
  }

  .desktop-padding {
    padding: 1.5rem !important;
  }
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {
  .touch-target {
    min-height: 44px !important;
    min-width: 44px !important;
  }

  .touch-padding {
    padding: 0.75rem !important;
  }

  /* Larger touch targets for buttons */
  button {
    min-height: 44px;
    min-width: 44px;
  }

  /* Better touch scrolling */
  .touch-scroll {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
  }
}

/* Enhanced Z.ai Style Animations */
@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.animate-slide-in-right {
  animation: slideInRight 0.3s ease-out forwards;
}

/* Tab Transition Animation */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fadeIn 0.2s ease-out forwards;
}

/* Enhanced Hover Effects */
.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* Button Hover Effects */
.btn-hover:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

/* Enhanced Pulse Animation */
@keyframes pulse-slow {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse-slow {
  animation: pulse-slow 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Smooth Transitions */
.transition-smooth {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Glass Effect */
.glass-effect {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Professional Tab Styling */
.tab-active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.tab-inactive {
  background: transparent;
  color: #6b7280;
  border: 1px solid #e5e7eb;
}

.tab-inactive:hover {
  background: #f3f4f6;
  color: #374151;
}

/* Fix for message content overflow */
.message-content {
  word-wrap: break-word;
  overflow-wrap: break-word;
  white-space: pre-wrap;
  max-width: 100%;
  overflow-x: auto;
}

.message-content pre {
  white-space: pre-wrap;
  word-wrap: break-word;
  overflow-wrap: break-word;
  max-width: 100%;
  overflow-x: auto;
  background: rgba(0, 0, 0, 0.05);
  padding: 1rem;
  border-radius: 0.5rem;
  margin: 0.5rem 0;
}

.message-content code {
  word-wrap: break-word;
  overflow-wrap: break-word;
  white-space: pre-wrap;
  max-width: 100%;
  display: block;
  overflow-x: auto;
}

/* Specific fix for HTML content display */
.html-content {
  max-width: 100%;
  overflow-x: auto;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.html-content pre,
.html-content code {
  white-space: pre-wrap;
  word-wrap: break-word;
  overflow-wrap: break-word;
  max-width: 100%;
  overflow-x: auto;
}

/* Ensure chat messages don't overflow */
.chat-message {
  max-width: 100%;
  overflow-x: auto;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

/* Fix for long text content */
.text-content {
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
  max-width: 100%;
}