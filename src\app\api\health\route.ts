import { NextResponse } from 'next/server'
import { DeepSeekService } from '@/lib/deepseek'
import { GeminiService } from '@/lib/gemini'

export async function GET() {
  try {
    const deepseek = new DeepSeekService()
    const gemini = new GeminiService()

    // Check health of both services
    const [deepseekHealth, geminiHealth] = await Promise.allSettled([
      deepseek.checkHealth(),
      gemini.checkHealth()
    ])

    const status = {
      timestamp: new Date().toISOString(),
      services: {
        deepseek: {
          status: deepseekHealth.status === 'fulfilled' && deepseekHealth.value ? 'healthy' : 'unhealthy',
          configured: !!process.env.DEEPSEEK_API_KEY,
          error: deepseekHealth.status === 'rejected' ? deepseekHealth.reason?.message : null
        },
        gemini: {
          status: geminiHealth.status === 'fulfilled' && geminiHealth.value ? 'healthy' : 'unhealthy',
          configured: !!process.env.GEMINI_API_KEY,
          error: geminiHealth.status === 'rejected' ? geminiHealth.reason?.message : null
        }
      },
      environment: {
        nodeEnv: process.env.NODE_ENV,
        maxTokens: process.env.MAX_TOKENS || '4000',
        temperature: process.env.TEMPERATURE || '0.7',
        rateLimitEnabled: !!process.env.RATE_LIMIT_REQUESTS_PER_MINUTE
      }
    }

    // Determine overall health
    const isHealthy = status.services.deepseek.status === 'healthy' || 
                     status.services.gemini.status === 'healthy'

    return NextResponse.json(
      {
        success: true,
        healthy: isHealthy,
        ...status
      },
      { status: isHealthy ? 200 : 503 }
    )

  } catch (error) {
    console.error('Health check error:', error)
    
    return NextResponse.json(
      {
        success: false,
        healthy: false,
        error: error instanceof Error ? error.message : 'Health check failed',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    )
  }
}
